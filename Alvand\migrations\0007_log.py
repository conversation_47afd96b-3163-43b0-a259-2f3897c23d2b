# Generated by Django 5.1.6 on 2025-02-26 18:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Alvand', '0006_permissions'),
    ]

    operations = [
        migrations.CreateModel(
            name='Log',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('userBackup', models.CharField(max_length=200)),
                ('errCode', models.CharField(max_length=100)),
                ('errMessage', models.TextField()),
                ('macAddress', models.TextField(blank=True, default=None, max_length=250, null=True)),
                ('ip', models.GenericIPAddressField()),
                ('byWho', models.CharField(default='Lotus', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.Foreign<PERSON>ey(db_column='user', on_delete=django.db.models.deletion.DO_NOTHING, to='Alvand.users')),
            ],
        ),
    ]
