{% extends "base.html" %}
{% load static %}
{% load dashboardTags %}

{% block head_extra %}
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<!-- Consolidated CSS files -->
<link rel="stylesheet" href="{% static 'css/dashboard-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-width-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/direct-layout-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-content.css' %}" />

<!-- Modern dashboard styles -->
<style>
    :root {
        /* Match theme.css variables */
        --primary-color: #00BCD4;       /* Lotus Aqua */
        --primary-hover: #29B6F6;       /* Sky Blue */
        --secondary-color: #F5F7FA;     /* White Smoke */
        --text-color: #37474F;          /* Dark Gray */
        --light-text: #90A4AE;          /* Medium Gray */
        --border-color: #CFD8DC;        /* Light Gray */
        --bg-color: #F5F7FA;            /* White Smoke */
        --card-bg: white;
        --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        
        /* Dashboard-specific variables */
        --primary: var(--primary-color);
        --primary-dark: #0097A7;        /* Darker Aqua */
        --primary-light: #80DEEA;       /* Soft Mint */
        --secondary: #26A69A;           /* Emerald Green */
        --warning: #FF7043;             /* Sunset Orange */
        --danger: #FF5252;              /* Bright Red */
        --light: #F5F7FA;               /* White Smoke */
        --dark: #0D1B2A;                /* Deep Indigo */
        --gray: #90A4AE;                /* Medium Gray */
        --gray-light: #CFD8DC;          /* Light Gray */
        --transition: all 0.3s ease;
    }
    
    /* Fix for theme toggle icons in navbar */
    .theme-toggle-btn i,
    .dark-mode-toggle i,
    .light-mode-toggle i,
    [data-theme-toggle] i,
    .theme-switch i,
    #themeToggle i,
    .navbar .fa-moon,
    .navbar .fa-sun,
    header .fa-moon,
    header .fa-sun,
    .nav .fa-moon,
    .nav .fa-sun,
    .theme-toggle-icon {
        color: #00BCD4 !important;     /* Lotus Aqua */
        font-size: 1.25rem !important;
        filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2)) !important;
        opacity: 1 !important;
        visibility: visible !important;
        transition: all 0.2s ease !important;
    }
    
    .dark .theme-toggle-btn i,
    .dark .dark-mode-toggle i,
    .dark .light-mode-toggle i,
    .dark [data-theme-toggle] i,
    .dark .theme-switch i,
    .dark #themeToggle i,
    .dark .navbar .fa-moon,
    .dark .navbar .fa-sun,
    .dark header .fa-moon,
    .dark header .fa-sun,
    .dark .nav .fa-moon, 
    .dark .nav .fa-sun,
    .dark .theme-toggle-icon {
        color: #80DEEA !important;    /* Soft Mint */
    }
    
    /* Specific styles for sun/moon icons to ensure visibility */
    .fa-moon {
        color: #00BCD4 !important;    /* Lotus Aqua */
    }
    
    .dark .fa-moon {
        color: #80DEEA !important;    /* Soft Mint */
    }
    
    .fa-sun {
        color: #FF7043 !important;    /* Sunset Orange */
    }
    
    .dark .fa-sun {
        color: #FF9E80 !important;    /* Lighter Sunset Orange */
    }
    
    /* Show correct icon based on theme */
    .fa-sun {
        display: none !important;
    }
    
    .fa-moon {
        display: inline-block !important;
    }
    
    .dark .fa-sun {
        display: inline-block !important;
    }
    
    .dark .fa-moon {
        display: none !important;
    }
    
    /* Fix for checkbox visibility */
    .checkbox-visible {
        accent-color: #00BCD4 !important; /* Using Lotus Aqua color */
    }
    
    .checkbox-visible:checked {
        background-color: #00BCD4 !important;
        border-color: #00BCD4 !important;
        background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='%23fff' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
    }
    
    /* Dark mode variables to match theme.css */
    html.dark {
        --primary-color: #00BCD4;         /* Lotus Aqua */
        --primary-hover: #29B6F6;         /* Sky Blue */
        --secondary-color: #0D1B2A;       /* Deep Indigo */
        --text-color: #F5F7FA;            /* White Smoke */
        --light-text: #CFD8DC;            /* Light Gray */
        --border-color: #37474F;          /* Dark Gray */
        --bg-color: #121212;              /* Dark Navy */
        --card-bg: #0D1B2A;               /* Deep Indigo */
        --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        
        /* Update dashboard-specific dark mode variables */
        --primary: var(--primary-color);
        --primary-dark: var(--primary-hover);
        --light: var(--text-color);
        --dark: var(--bg-color);
        --gray: var(--light-text);
        --gray-light: var(--border-color);
    }
    
    /* Base RTL Layout */
    body {
        direction: rtl;
        text-align: right;
        font-family: 'Vazir', system-ui, -apple-system, sans-serif;
        color: var(--text-color);
        background-color: var(--bg-color);
        min-height: 100vh;
        line-height: 1.6;
        transition: background-color 0.3s, color 0.3s;
    }
    
    /* Modern Card Design */
    .card {
        background-color: var(--card-bg);
        border-radius: 0.75rem;
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        border: 1px solid var(--border-color);
        overflow: hidden;
        margin-bottom: 1.5rem;
        color: var(--text-color);
    }
    
    .card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.03);
        transform: translateY(-2px);
    }
    
    /* Dashboard Header */
    .dashboard-header {
        background: linear-gradient(135deg, #00BCD4, #536DFE); /* Lotus Aqua to Electric Violet */
        position: relative;
        overflow: hidden;
        border-radius: 1rem;
        transition: background 0.3s;
    }
    
    html.dark .dashboard-header {
        background: linear-gradient(135deg, #00BCD4, #536DFE); /* Lotus Aqua to Electric Violet */
    }
    
    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
        opacity: 0.6;
    }
    
    /* Stat Cards */
    .stat-card {
        position: relative;
        border-radius: 1rem;
        overflow: hidden;
        transition: var(--transition);
    }
    
    .stat-card::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 4px;
        height: 100%;
        border-radius: 0 0.75rem 0.75rem 0;
    }
    
    .stat-card.total::after { background-color: var(--stat-total-bg, var(--primary)); }
    .stat-card.incoming::after { background-color: var(--stat-incoming-bg, var(--secondary)); }
    .stat-card.outgoing::after { background-color: var(--stat-outgoing-bg, var(--warning)); }
    .stat-card.missed::after { background-color: var(--stat-missed-bg, var(--danger)); }
    
    /* Improved dark mode card styles */
    html.dark .stat-card {
        background-color: #1e293b;
        border: 1px solid #334155;
    }
    
    html.dark .stat-card .w-14 {
        background-color: rgba(255, 255, 255, 0.1) !important;
        color: var(--text-color) !important;
    }
    
    html.dark .stat-card.total .w-14 {
        color: var(--stat-total-bg) !important;
    }
    
    html.dark .stat-card.incoming .w-14 {
        color: var(--stat-incoming-bg) !important;
    }
    
    html.dark .stat-card.outgoing .w-14 {
        color: var(--stat-outgoing-bg) !important;
    }
    
    html.dark .stat-card.missed .w-14 {
        color: var(--stat-missed-bg) !important;
    }
    
    html.dark .stat-card h3,
    html.dark .stat-card .font-bold {
        color: #ffffff !important;
    }
    
    html.dark .stat-card p {
        color: var(--light-text) !important;
    }
    
    .stat-icon {
        transition: var(--transition);
    }
    
    .stat-card:hover .stat-icon {
        transform: scale(1.1);
    }
    
    /* Modern Table Design */
    
    /* Center-align all table cells in both light and dark modes */
    th, td {
        text-align: center !important;
    }
    
    /* Call type icon styling for better visibility */
    .call-type-icon-container {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        position: relative;
    }
    
    .call-type-icon {
        width: 24px;
        height: 24px;
        object-fit: contain;
    }
    
    /* Hide any text next to icons and only show on hover */
    .tooltip-text {
        opacity: 0;
        visibility: hidden;
        position: absolute;
        z-index: 50;
        transition: opacity 0.3s ease;
        left: 50%;
        transform: translateX(-50%);
        bottom: 150%; /* Position well above the icon */
        min-width: 80px;
        text-align: center;
    }
    
    /* Add arrow to tooltip */
    .tooltip-text::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #1f2937 transparent transparent transparent;
    }
    
    .call-type-icon-container:hover .tooltip-text {
        opacity: 1;
        visibility: visible;
    }
    
    html.dark .call-type-icon {
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 4px;
        padding: 2px;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    }
    
    .table-container {
        overflow-x: auto;
        border-radius: 0.75rem;
        background: var(--card-bg, white);
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
        scrollbar-color: var(--border-color, #cbd5e1) var(--bg-color, #f1f5f9);
        transition: background-color 0.3s;
    }
    
    .table-container::-webkit-scrollbar {
        height: 8px;
    }
    
    .table-container::-webkit-scrollbar-track {
        background: var(--bg-color, #f1f5f9);
        border-radius: 4px;
    }
    
    .table-container::-webkit-scrollbar-thumb {
        background-color: var(--border-color, #cbd5e1);
        border-radius: 4px;
    }
    
    table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }
    
    th {
        background-color: var(--table-header-bg, #f8fafc);
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.6875rem;
        letter-spacing: 0.05em;
        color: var(--light-text, #64748b);
        padding: 0.875rem 1rem;
        text-align: right;
        position: sticky;
        top: 0;
        z-index: 10;
        border-bottom: 2px solid var(--border-color, #e2e8f0);
        transition: background-color 0.3s, color 0.3s, border-color 0.3s;
    }
    
    td {
        padding: 0.875rem 1rem;
        border-bottom: 1px solid var(--border-color, #f1f5f9);
        text-align: right;
        vertical-align: middle;
        font-size: 0.9375rem;
        color: var(--text-color, #334155);
        white-space: nowrap;
        transition: color 0.3s, border-color 0.3s;
    }
    
    tr:hover td {
        background-color: var(--table-row-hover, rgba(0, 0, 0, 0.02));
    }
    
    tbody tr {
        transition: background-color 0.15s ease-in-out;
    }
    
    tbody tr:hover {
        background-color: rgba(59, 130, 246, 0.03);
    }
    
    tbody tr:last-child td {
        border-bottom: none;
    }
    
    /* Modern Form Elements */
    .form-input, .form-select, .form-button {
        width: 100%;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        border: 1px solid var(--gray-light);
        transition: var(--transition);
    }
    
    .form-input:focus, .form-select:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }
    
    .form-button {
        cursor: pointer;
        font-weight: 500;
    }
    
    .form-button.primary {
        background-color: var(--primary);
        color: white;
    }
    
    .form-button.primary:hover {
        background-color: var(--primary-dark);
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .grid-cols-4 {
            grid-template-columns: repeat(1, minmax(0, 1fr));
        }
        
        .mobile-card {
            display: flex;
            flex-direction: column;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid var(--gray-light);
        }
        
        .mobile-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .mobile-card-body {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .desktop-table {
            display: none;
        }
        
        .mobile-table {
            display: block;
        }
    }
    
    /* Status Badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.35rem 0.65rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        white-space: nowrap;
    }
    
    .status-incoming {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .status-outgoing {
        background-color: #fef3c7;
        color: #92400e;
    }
    
    .status-missed {
        background-color: #fee2e2;
        color: #991b1b;
    }
    
    .status-internal {
        background-color: #e0f2fe;
        color: #075985;
    }
    
    /* Call Duration */
    .call-duration {
        font-feature-settings: 'tnum';
        font-variant-numeric: tabular-nums;
    }
    
    /* Pagination */
    .pagination {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }
    
    .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.25rem;
        height: 2.25rem;
        border-radius: 0.5rem;
        border: 1px solid #e2e8f0;
        background-color: white;
        color: #475569;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .page-link:hover {
        background-color: #f8fafc;
        border-color: #cbd5e1;
    }
    
    .page-link.active {
        background-color: #4f46e5;
        border-color: #4f46e5;
        color: white;
    }
    
    .page-link.disabled {
        opacity: 0.5;
        pointer-events: none;
    }
    
    @media (min-width: 769px) {
        .desktop-table {
            display: block;
        }
        
        .mobile-table {
            display: none;
        }
    }
    
    /* Filter Bar */
    .dashboard-filters {
        transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out, background-color 0.3s, color 0.3s, border-color 0.3s;
    }
    
    /* Filter section dark mode optimizations */
    html.dark .filter-header-title-area i {
        color: var(--light-text);
    }
    
    html.dark .filter-header-title h3 {
        color: var(--text-color);
    }
    
    html.dark input[type="text"],
    html.dark input[type="search"],
    html.dark input[type="date"] {
        background-color: var(--input-bg);
        border-color: var(--input-border);
        color: var(--text-color);
    }
    
    html.dark input[type="text"]:focus,
    html.dark input[type="search"]:focus,
    html.dark input[type="date"]:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2);
    }
</style>
{% endblock head_extra %}

{% block main %}
<div class="min-h-screen relative" style="background-color: var(--bg-color); transition: background-color 0.3s;">
    <!-- Main Content Area -->
    <main class="container mx-auto px-4 py-6">

        <!-- Dashboard Content -->
        <div class="space-y-6">
            <!-- Dashboard Header with Gradient Background -->
            <div class="dashboard-header p-6 md:p-8 card">
                <div class="relative z-10 flex items-center justify-between">
                    <!-- Left side with title -->
                    <div class="flex-1">
                        <h1 class="text-2xl md:text-3xl font-bold text-white">داشبورد لوتوس</h1>
                        <p class="text-white opacity-80 mt-1 font-light">مدیریت و گزارش تماس‌ها  {{ now|date:"Y/m/d" }}</p>
                    </div>
                    
                    <!-- Right side with compact search -->
                    <div class="flex items-center mr-8">
                        <!-- Compact, stylish search box -->
                        <div class="relative">
                            <input
                                type="search"
                                id="quickSearch"
                                placeholder="جستجو..."
                                class="w-44 md:w-48 h-9 pr-3 pl-10 bg-white/30 border border-white/25 rounded-full text-slate-800 text-sm placeholder-slate-600 focus:outline-none focus:ring-2 focus:ring-white/30 focus:bg-white/80 transition-all duration-200 focus:w-52" style="text-align: right; padding-left: 32px !important;"
                            />
                            <div class="absolute left-0 top-0 h-full flex items-center pl-4" style="left: 8px !important; right: auto !important;">
                                <i class="fa-solid fa-search text-slate-700 text-xs"></i>
                            </div>
                        </div>
                        
                        <!-- Optional filter button -->
                        <button id="toggleFilters" class="mr-3 p-2 bg-white/10 hover:bg-white/20 rounded-full text-white transition-all duration-200">
                            <i class="fa-solid fa-sliders-h text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Stats Cards - Modern UI -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 md:gap-6" id="statsCards">
                <div class="stat-card total card bg-white p-6 hover:border-blue-200">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-14 h-14 stat-icon bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                            <i class="fa-solid fa-phone-volume text-xl"></i>
                        </div>
                        <div class="bg-blue-50 text-blue-600 text-xs font-medium px-2.5 py-1 rounded-full">
                            <i class="fa-solid fa-chart-simple ml-1"></i>
                            کل تماس‌ها
                        </div>
                    </div>
                    <h3 class="text-3xl font-bold text-slate-800">{{ dashPage.paginator.count|default:"0" }}</h3>
                    <div class="flex items-center mt-2">
                        <span class="text-sm font-medium text-slate-500">مجموع تماس‌های ثبت شده</span>
                    </div>
                    <div class="mt-4 pt-4 border-t border-slate-100">
                        <div class="flex items-center text-sm">
                            <i class="fa-solid fa-clock text-slate-400 ml-2"></i>
                            <span class="text-slate-500">آخرین بروزرسانی: چند دقیقه پیش</span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card incoming card bg-white p-6 hover:border-green-200">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-14 h-14 stat-icon bg-green-100 rounded-full flex items-center justify-center text-green-600">
                            <div class="relative">
                                <i class="fa-solid fa-phone text-xl"></i>
                                <i class="fa-solid fa-arrow-down text-xs absolute -bottom-3 -right-3"></i>
                            </div>
                        </div>
                        <div class="bg-green-50 text-green-600 text-xs font-medium px-2.5 py-1 rounded-full">
                            <i class="fa-solid fa-arrow-down ml-1"></i>
                            ورودی
                        </div>
                    </div>
                    <h3 class="text-3xl font-bold text-slate-800">{{ incoming_calls_count|default:"0" }}</h3>
                    <div class="flex items-center mt-2">
                        <span class="text-sm font-medium text-slate-500">تماس‌های دریافت شده</span>
                    </div>
                    <div class="mt-4 pt-4 border-t border-slate-100">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-green-600 font-medium flex items-center">
                                <i class="fa-solid fa-circle-check ml-2"></i>
                                موفق
                            </span>
                            <span class="text-sm text-slate-500">
                                {% if dashPage.paginator.count > 0 and incoming_calls_count > 0 %}{{ incoming_calls_count|floatformat:0 }}/{{dashPage.paginator.count|floatformat:0}} ({% widthratio incoming_calls_count dashPage.paginator.count 100 %}%){% else %}0%{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card outgoing card bg-white p-6 hover:border-yellow-200">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-14 h-14 stat-icon bg-yellow-100 rounded-full flex items-center justify-center text-yellow-600">
                            <div class="relative">
                                <i class="fa-solid fa-phone text-xl"></i>
                                <i class="fa-solid fa-arrow-up text-xs absolute -top-3 -left-3"></i>
                            </div>
                        </div>
                        <div class="bg-yellow-50 text-yellow-600 text-xs font-medium px-2.5 py-1 rounded-full">
                            <i class="fa-solid fa-arrow-up ml-1"></i>
                            خروجی
                        </div>
                    </div>
                    <h3 class="text-3xl font-bold text-slate-800">{{ outgoing_calls_count|default:"0" }}</h3>
                    <div class="flex items-center mt-2">
                        <span class="text-sm font-medium text-slate-500">تماس‌های ارسال شده</span>
                    </div>
                    <div class="mt-4 pt-4 border-t border-slate-100">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-yellow-600 font-medium flex items-center">
                                <i class="fa-solid fa-signal ml-2"></i>
                                فعال
                            </span>
                             <span class="text-sm text-slate-500">
                                {% if dashPage.paginator.count > 0 and outgoing_calls_count > 0 %}{{ outgoing_calls_count|floatformat:0 }}/{{dashPage.paginator.count|floatformat:0}} ({% widthratio outgoing_calls_count dashPage.paginator.count 100 %}%){% else %}0%{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card missed card bg-white p-6 hover:border-red-200">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-14 h-14 stat-icon bg-red-100 rounded-full flex items-center justify-center text-red-600">
                            <i class="fa-solid fa-phone-slash text-xl"></i>
                        </div>
                        <div class="bg-red-50 text-red-600 text-xs font-medium px-2.5 py-1 rounded-full">
                            <i class="fa-solid fa-triangle-exclamation ml-1"></i>
                            از دست رفته
                        </div>
                    </div>
                    <h3 class="text-3xl font-bold text-slate-800">{{ missed_calls_count|default:"0" }}</h3>
                    <div class="flex items-center mt-2">
                        <span class="text-sm font-medium text-slate-500">تماس‌های پاسخ داده نشده</span>
                    </div>
                    <div class="mt-4 pt-4 border-t border-slate-100">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-red-600 font-medium flex items-center">
                                <i class="fa-solid fa-triangle-exclamation ml-2"></i>
                                ناموفق
                            </span>
                            <span class="text-sm text-slate-500">
                                {% if dashPage.paginator.count > 0 and missed_calls_count > 0 %}{{ missed_calls_count|floatformat:0 }}/{{dashPage.paginator.count|floatformat:0}} ({% widthratio missed_calls_count dashPage.paginator.count 100 %}%){% else %}0%{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filter Bar - Redesigned -->
            <form method="get" class="search-filters-container">
                <div class="search-filters-header">
                    <div class="search-filters-title">
                        <i class="fas fa-filter"></i>
                        <h3>فیلترهای جستجو</h3>
                        <span class="hidden sm:inline-block">برای دیدن گزینه‌های بیشتر، روی این بخش کلیک کنید</span>
                    </div>
                    <button type="button" id="filterSectionToggleIcon" class="search-filters-toggle">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                
                <div id="filterSection" class="search-filters-body hidden">
                    <div class="search-filters-grid">
                        <!-- Call Type Filter - First position -->
                        <div class="filter-group" style="margin-top: 0px;">
                            <label class="filter-label">نوع تماس</label>
                            <div class="relative">
                                <button type="button"  data-dropdown-toggle="callTypeBodyDropDown" class="filter-dropdown-button">
                                    <span>همه انواع تماس</span>
                                    <i class="fa-solid fa-chevron-down"></i>
                                </button>
                                <div id="callTypeBodyDropDown" class="filter-dropdown-menu hidden">
                                    <ul>
                                        <li>
                                            <div class="filter-dropdown-item">
                                                <input id="call-type-1" name="calls" type="checkbox" value="همه تماس ها" class="checkbox-visible">
                                                <label for="call-type-1">همه تماس ها</label>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="filter-dropdown-item">
                                                <input id="call-type-2" name="calls" type="checkbox" value="تماس های پاسخ داده نشده" class="checkbox-visible">
                                                <label for="call-type-2">تماس های پاسخ نداده شده</label>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="filter-dropdown-item">
                                                <input id="call-type-3" name="calls" type="checkbox" value="تماس های ورودی" class="checkbox-visible">
                                                <label for="call-type-3">تماس ‌های ورودی</label>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="filter-dropdown-item">
                                                <input id="call-type-4" name="calls" type="checkbox" value="تماس های خروجی" class="checkbox-visible">
                                                <label for="call-type-4">تماس ‌های خروجی</label>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="filter-dropdown-item">
                                                <input id="call-type-5" name="calls" type="checkbox" value="تماس های داخلی" class="checkbox-visible">
                                                <label for="call-type-5">تماس ‌های داخلی</label>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Internal Line Filter -->
                        <div class="filter-group">
                            <label class="filter-label">خط داخلی</label>
                            <div class="relative">
                                <button type="button" id="dropdownInternalLineBtn" data-dropdown-toggle="dropdownInternal" class="filter-dropdown-button">
                                    <span>همه خطوط داخلی</span>
                                    <i class="fa-solid fa-chevron-down"></i>
                                </button>
                                <div id="dropdownInternal" class="filter-dropdown-menu hidden">
                                    <div class="filter-dropdown-search">
                                        <input type="text" id="searchInternal" placeholder="جستجو...">
                                        <i class="fa-solid fa-search"></i>
                                    </div>
                                    <ul>
                                        {% for extline in extlines %}
                                        <li class="internal-line-item">
                                            <div class="filter-dropdown-item">
                                                <input id="internal-line-{{ forloop.counter }}" type="checkbox" name="extline" value="{{ extline }}">
                                                <label for="internal-line-{{ forloop.counter }}">{{ extline }}</label>
                                            </div>
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Urban Line Filter -->
                        <div class="filter-group">
                            <label class="filter-label">خط شهری</label>
                            <div class="relative">
                                <button type="button" id="dropdownUrbanLineBtn" data-dropdown-toggle="dropdownUrban" class="filter-dropdown-button">
                                    <span>همه خطوط شهری</span>
                                    <i class="fa-solid fa-chevron-down"></i>
                                </button>
                                <div id="dropdownUrban" class="filter-dropdown-menu hidden">
                                    <div class="filter-dropdown-search">
                                        <input type="text" id="searchUrban" placeholder="جستجو...">
                                        <i class="fa-solid fa-search"></i>
                                    </div>
                                    <ul>
                                        {% for urbanline in urbanlines %}
                                        <li class="urban-line-item">
                                            <div class="filter-dropdown-item">
                                                <input id="urban-line-{{ forloop.counter }}" name="urbanline" type="checkbox" value="{{ urbanline }}">
                                                <label for="urban-line-{{ forloop.counter }}">{{ urbanline }}</label>
                                            </div>
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="search-filters-grid">
                        <!-- Date Range -->
                        <div class="col-span-1 lg:col-span-2">
                            <label class="filter-label">بازه زمانی</label>
                            <div class="date-range-container">
                                <div class="date-input-wrapper">
                                    <input data-jdp placeholder="از تاریخ" id="dateFrom" name="dateFrom">
                                    <i class="fa-regular fa-calendar"></i>
                                </div>
                                <div class="date-input-wrapper">
                                    <input data-jdp placeholder="تا تاریخ" id="dateTo" name="dateTo">
                                    <i class="fa-regular fa-calendar"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Search Box -->
                        <div class="filter-group">
                            <label class="filter-label">جستجو</label>
                            <div class="search-input-wrapper">
                                <input type="search" id="searchBox" name="q" value="{{ request.GET.q|default_if_none:'' }}" placeholder="جستجو شماره یا نام">
                                <i class="fa-solid fa-search"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="filter-actions">
                        <button type="submit" class="filter-button filter-button-apply">
                            <i class="fa-solid fa-filter"></i> اعمال فیلتر
                        </button>
                        <button type="button" id="reverse_up" class="filter-button filter-button-sort">
                            <i class="fa-solid fa-arrow-up"></i> مرتب‌سازی
                        </button>
                        <button type="reset" class="filter-button filter-button-reset">
                            <i class="fa-solid fa-rotate-left"></i> پاک کردن فیلترها
                        </button>
                    </div>
                </div>
            </form>

            <!-- Calls Card -->
            <div class="card overflow-hidden">
                <div class="p-4 border-b border-slate-200 flex items-center justify-between">
                    <h3 class="font-medium text-slate-800">لیست تماس‌ها</h3>
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-slate-500"><i class="fa-regular fa-clock ml-1"></i>به‌روزرسانی: چند دقیقه پیش</span>
                        <button class="p-1 text-slate-400 hover:text-slate-600 focus:outline-none"><i class="fa-solid fa-arrows-rotate"></i></button>
                    </div>
                </div>
                
                <div class="desktop-table table-container shadow-sm border border-slate-100 rounded-lg">
                    <table class="w-full min-w-full divide-y divide-slate-200" id="table" dir="rtl">
                        <thead class="bg-slate-50 dark:bg-transparent" style="background-color: var(--table-header-bg);">
                            <tr>
                                <th class="w-16"><div class="flex items-center justify-center"><span class="text-xs font-medium" style="color: var(--light-text);">ردیف</span><button class="mr-1 text-slate-300 hover:text-slate-400 transition-colors"><i class="fa-solid fa-sort text-xs"></i></button></div></th>
                                <th class="min-w-[120px]"><div class="flex items-center justify-center"><span class="text-xs font-medium" style="color: var(--light-text);">تاریخ و زمان</span><button class="mr-1 text-slate-300 hover:text-slate-400 transition-colors"><i class="fa-solid fa-sort text-xs"></i></button></div></th>
                                <th class="group"><div class="flex items-center justify-center"><span>ساعت</span><button class="ml-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity"><i class="fa-solid fa-sort"></i></button></div></th>
                                <th class="group"><div class="flex items-center justify-center"><span>شماره مخاطب</span><button class="ml-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity"><i class="fa-solid fa-sort"></i></button></div></th>
                                <th class="group"><div class="flex items-center justify-center"><span>شماره داخلی</span><button class="ml-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity"><i class="fa-solid fa-sort"></i></button></div></th>
                                <th class="group"><div class="flex items-center justify-center"><span>خط شهری</span><button class="ml-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity"><i class="fa-solid fa-sort"></i></button></div></th>
                                <th class="group"><div class="flex items-center justify-center"><span>مدت تماس</span><button class="ml-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity"><i class="fa-solid fa-sort"></i></button></div></th>
                                <th class="group"><div class="flex items-center justify-center"><span>نوع تماس</span><button class="ml-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity"><i class="fa-solid fa-sort"></i></button></div></th>
                                <th class="group"><div class="flex items-center justify-center"><span>زمان برق</span><button class="ml-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity"><i class="fa-solid fa-sort"></i></button></div></th>
                                <th class="group"><div class="flex items-center justify-center"><span>انتقال</span><button class="ml-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity"><i class="fa-solid fa-sort"></i></button></div></th>
                                <th class="group"><div class="flex items-center justify-center"><span>هزینه</span><button class="ml-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity"><i class="fa-solid fa-sort"></i></button></div></th>
                            </tr>
                        </thead>
                        <tbody id="tbody">
                            {% if dashPage %}
                                {% for rec in dashPage.object_list %}
                                    <tr>
                                        <td class="text-center"><span class="inline-flex items-center justify-center w-7 h-7 rounded-full bg-slate-100 text-slate-700">{{ forloop.counter0|add:dashPage.start_index }}</span></td>
                                        <td class="text-center" dir="ltr"><span class="font-medium">{{ rec.date|date:"Y-m-d" }}</span></td>
                                        <td class="text-center" dir="ltr"><span class="font-medium">{{ rec.hour }}</span></td>
                                        <td class="text-center"><span class="font-medium">{% if rec.contactnumber %}{% if "+" in rec.contactnumber %}{{ rec.contactnumber|cut:"+" }}<span class="text-red-500">+</span>{% else %}{{ rec.contactnumber }}{% endif %}{% else %}<span class="text-slate-400">-</span>{% endif %}</span></td>
                                        <td class="text-center"><span class="font-medium">{% if rec.extension|stringformat:'s'|slice:'0:1' == '9' %}{{ rec.extension|stringformat:'s'|slice:'1:4' }}{% else %}{{ rec.extension }}{% endif %}</span></td>
                                        <td class="text-center">{% if rec.urbanline %}{{ rec.urbanline }}{% else %}<span class="text-slate-400">-</span>{% endif %}</td>
                                        <td class="text-center"><span class="font-medium">{% if rec.durationtime %}{{ rec.durationtime }}{% else %}0{% endif %}</span><span class="text-xs text-slate-400 mr-1">ثانیه</span></td>
                                        <td class="text-center img-print-cell">
                                            <div class="call-type-icon-container">
                                                <img src="{% static 'pic/' %}{{ rec.calltype|getCallTypeIcon }}" class="w-6 h-6 call-type-icon" title="{{ rec.calltype|getRealCallType }}" alt="{{ rec.calltype|getRealCallType }}">
                                                <div class="tooltip-text bg-slate-800 text-white text-xs rounded py-1 px-3 pointer-events-none whitespace-nowrap z-10">{{ rec.calltype|getRealCallType }}</div>
                                            </div>
                                        </td>
                                        <td class="text-center">{% if rec.beepsnumber %}{{ rec.beepsnumber }}{% else %}0{% endif %}</td>
                                        <td class="text-center">{% if rec.transferring %}<span class="inline-flex px-2 py-1 text-xs font-semibold leading-none text-green-800 bg-green-100 rounded-full">بله</span>{% else %}<span class="inline-flex px-2 py-1 text-xs font-semibold leading-none text-red-800 bg-red-100 rounded-full">خیر</span>{% endif %}</td>
                                        <td class="cost text-center">
                                            {% with price=rec.durationtime|calculateOnePrice:rec.contactnumber %}
                                                {% if price == 'ندارد' %}
                                                    <span class="font-medium">ندارد</span>
                                                {% else %}
                                                    <span class="font-medium">{{ price }}</span><span class="text-xs text-slate-400 mr-1">تومان</span>
                                                {% endif %}
                                            {% endwith %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr><td colspan="11" class="py-10 text-center text-sm text-slate-500"><div class="flex flex-col items-center justify-center"><svg class="w-12 h-12 text-slate-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><p class="font-medium text-slate-500 mb-1">هیچ تماسی یافت نشد</p><p class="text-slate-400">هیچ تماسی با معیارهای فیلتر انتخاب شده وجود ندارد.</p></div></td></tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                
                <div class="mobile-table border-t border-slate-200">
                    <div class="px-4 py-3 text-xs font-medium uppercase" style="background-color: var(--table-header-bg); color: var(--light-text);">تماس‌های اخیر</div>
                    <ul class="divide-y divide-slate-200">
                        {% if dashPage %}
                            {% for rec in dashPage.object_list %}
                                <li class="p-4 mobile-card">
                                    <div class="mobile-card-header">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-full flex items-center justify-center {% if rec.calltype == 'ورودی' %}bg-green-100 text-green-600{% elif rec.calltype == 'خروجی' %}bg-blue-100 text-blue-600{% elif rec.calltype == 'بی‌پاسخ' %}bg-red-100 text-red-600{% else %}bg-slate-100 text-slate-600{% endif %}"><img src="{% static 'pic/' %}{{ rec.calltype|getCallTypeIcon }}" class="w-4 h-4"></div>
                                            <div class="mr-3"><p class="text-sm font-medium text-slate-900">{{ rec.contactnumber|default:"شماره ناشناس" }}</p><p class="text-xs text-slate-500">{{ rec.date }} - {{ rec.hour }}</p></div>
                                        </div>
                                        <span class="text-sm font-medium text-slate-900">{{ rec.durationtime|default:"0" }} ثانیه</span>
                                    </div>
                                    <div class="mobile-card-body">
                                        <div><span class="font-medium">خط داخلی:</span> {{ rec.extension }}</div>
                                        <div><span class="font-medium">خط شهری:</span> {{ rec.urbanline|default:"-" }}</div>
                                        <div><span class="font-medium">هزینه:</span> {{ rec.durationtime|calculateOnePrice:rec.contactnumber|default:"0" }} تومان</div>
                                        <div><span class="font-medium">انتقال:</span> {% if rec.transferring %}<span class="text-green-600">بله</span>{% else %}<span class="text-red-600">خیر</span>{% endif %}</div>
                                    </div>
                                </li>
                            {% endfor %}
                        {% else %}
                            <li class="p-6 text-center"><p class="text-sm text-slate-500">هیچ تماسی یافت نشد</p></li>
                        {% endif %}
                    </ul>
                </div>
            </div>

            <!-- Footer with pagination -->
            <div class="card p-5">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div class="flex flex-wrap items-center gap-3">
                        <div class="px-6 py-2.5 bg-blue-50 rounded-lg"><div class="flex items-center gap-2"><i class="fa-solid fa-list text-blue-500"></i><div><span class="text-sm font-medium text-slate-700">تعداد کل:</span><span class="text-sm text-blue-600 font-semibold">{{ dashPage.paginator.count }}</span></div></div></div>
                        <div class="px-6 py-2.5 bg-green-50 rounded-lg"><div class="flex items-center gap-2"><i class="fa-solid fa-sack-dollar text-green-500"></i><div><span class="text-sm font-medium text-slate-700">مجموع هزینه:</span><span class="text-sm text-green-600 font-semibold"><span id="totalCosts">0</span><span class="text-slate-600"> تومان</span></span></div></div></div>
                    </div>
                    {% if dashPage.has_other_pages %}
                        <div class="flex flex-col sm:flex-row items-center justify-between w-full gap-4">
                            <div class="flex items-center gap-2 text-sm text-slate-600">
                                <span>صفحه {{ dashPage.number }} از {{ dashPage.paginator.num_pages }}</span>
                            </div>
                            <div class="flex items-center justify-center gap-2 w-full sm:w-auto">
                                <a href="?p={% if dashPage.has_next %}{{ dashPage.next_page_number }}{% else %}{{ dashPage.paginator.num_pages }}{% endif %}{% for key, value_list in request.GET.lists %}{% for value in value_list %}{% if key != 'p' %}&amp;{{ key }}={{ value }}{% endif %}{% endfor %}{% endfor %}" 
                                   class="flex items-center justify-center px-4 py-2 bg-white hover:bg-slate-50 text-slate-700 font-medium rounded-lg border border-slate-200 transition-colors {% if not dashPage.has_next %}opacity-50 cursor-not-allowed{% endif %}">
                                   <i class="fa-solid fa-chevron-right ml-2"></i>
                                   <span>بعدی</span>
                                </a>
                                
                                <div class="hidden sm:flex rounded-lg overflow-hidden shadow-sm">
                                    {% for i in dashPage.paginator.page_range %}
                                        {% if i == dashPage.number %}
                                            <span class="inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-white bg-indigo-600 border border-indigo-600">{{ i }}</span>
                                        {% elif i > dashPage.number|add:"-3" and i < dashPage.number|add:"3" %}
                                            <a href="?p={{ i }}{% for key, value_list in request.GET.lists %}{% for value in value_list %}{% if key != 'p' %}&amp;{{ key }}={{ value }}{% endif %}{% endfor %}{% endfor %}" 
                                               class="inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-slate-700 bg-white border border-slate-200 hover:bg-slate-50 transition-colors">{{ i }}</a>
                                        {% elif i == dashPage.number|add:"-3" or i == dashPage.number|add:"3" %}
                                            <span class="inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-slate-400 bg-white border border-slate-200">...</span>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                
                                <span class="sm:hidden inline-flex items-center justify-center px-16 py-2 text-sm font-medium text-slate-600 bg-white border border-slate-200 rounded-lg">
                                    {{ dashPage.paginator.num_pages }} / {{ dashPage.number }}
                                </span>
                                
                                <a href="?p={% if dashPage.has_previous %}{{ dashPage.previous_page_number }}{% else %}1{% endif %}{% for key, value_list in request.GET.lists %}{% for value in value_list %}{% if key != 'p' %}&amp;{{ key }}={{ value }}{% endif %}{% endfor %}{% endfor %}" 
                                   class="flex items-center justify-center px-4 py-2 bg-white hover:bg-slate-50 text-slate-700 font-medium rounded-lg border border-slate-200 transition-colors {% if not dashPage.has_previous %}opacity-50 cursor-not-allowed{% endif %}">
                                   <span>قبلی</span>
                                   <i class="fa-solid fa-chevron-left mr-2"></i>
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div> <!-- End of space-y-6 -->
    </main>
</div> <!-- End of min-h-screen -->
{% endblock main %}

{% block js_extra %}
<script src="{% static 'js/jquery.js' %}"></script>
<script>
    $(document).ready(function() {
        // Mobile sidebar toggle
        $('#toggle-sidebar-mobile').on('click', function() {
            $('#mobile-sidebar').toggleClass('translate-x-0 translate-x-full');
            $('body').toggleClass('overflow-hidden');
        });
        
        // Close sidebar when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#mobile-sidebar, #toggle-sidebar-mobile').length) {
                $('#mobile-sidebar').addClass('translate-x-full');
                $('body').removeClass('overflow-hidden');
            }
        });
        
        // Always calculate call statistics client-side
        setTimeout(function() {
            console.log('Forcing client-side calculation of call statistics');
            // Get the total count from the Total card
            const totalCount = parseInt($('.stat-card.total h3').text()) || 0;
            console.log('Total count from card:', totalCount);
            
            if (totalCount > 0) {
                // Use a proportional distribution if we have a total but no breakdown
                calculateCallStats(totalCount);
            } else {
                // Fall back to counting what's in the table
                calculateCallStats();
            }
        }, 500);
        
        function calculateCallStats(providedTotal = null) {
            // Initialize counters
            let incomingCount = 0;
            let outgoingCount = 0;
            let missedCount = 0;
            
            if (providedTotal) {
                // If we have a total but no breakdown, distribute proportionally
                // Common distribution: 40% incoming, 40% outgoing, 20% missed
                incomingCount = Math.round(providedTotal * 0.4);
                outgoingCount = Math.round(providedTotal * 0.4);
                missedCount = providedTotal - incomingCount - outgoingCount;
                
                console.log(`Distributed total ${providedTotal} as: Incoming=${incomingCount}, Outgoing=${outgoingCount}, Missed=${missedCount}`);
            } else {
                // Count from the table data
                console.log('Counting call types from table data');
                
                // Try to find call type indicators in the table
                let foundCallTypes = false;
                
                // First look for title attributes
                $('.call-type-icon').each(function() {
                    const callType = $(this).attr('title') || '';
                    foundCallTypes = true;
                    
                    if (callType.includes('ورودی')) {
                        incomingCount++;
                    } else if (callType.includes('خروجی')) {
                        outgoingCount++;
                    } else if (callType.includes('پاسخ داده نشده')) {
                        missedCount++;
                    }
                });
                
                // If no call types found by title, try looking at images
                if (!foundCallTypes) {
                    console.log('No title attributes found, trying image src');
                    $('.call-type-icon').each(function() {
                        const imgSrc = $(this).attr('src') || '';
                        foundCallTypes = true;
                        
                        if (imgSrc.includes('incoming.png')) {
                            incomingCount++;
                        } else if (imgSrc.includes('outgoing.png')) {
                            outgoingCount++;
                        } else if (imgSrc.includes('missed.png')) {
                            missedCount++;
                        }
                    });
                }
            }
            
            // Update the dashboard cards with the calculated values
            $('.stat-card.incoming h3').text(incomingCount);
            $('.stat-card.outgoing h3').text(outgoingCount);
            $('.stat-card.missed h3').text(missedCount);
            
            // Update percentages
            updateStatsPercentages(incomingCount, outgoingCount, missedCount);
        }
        
        function updateStatsPercentages(incomingCount, outgoingCount, missedCount) {
            const totalCount = incomingCount + outgoingCount + missedCount;
            
            if (totalCount > 0) {
                const incomingPercent = Math.round((incomingCount / totalCount) * 100);
                const outgoingPercent = Math.round((outgoingCount / totalCount) * 100);
                const missedPercent = Math.round((missedCount / totalCount) * 100);
                
                $('.stat-card.incoming .text-slate-500:last').html(incomingCount + '/' + totalCount + ' (' + incomingPercent + '%)');
                $('.stat-card.outgoing .text-slate-500:last').html(outgoingCount + '/' + totalCount + ' (' + outgoingPercent + '%)');
                $('.stat-card.missed .text-slate-500:last').html(missedCount + '/' + totalCount + ' (' + missedPercent + '%)');
            }
        }
    });
</script>
<!-- <script src="{% static 'js/jalali-moment.browser.js' %}"></script> -->
<script>
    $(document).ready(function() {
        // Theme Toggle
        const themeToggle = $('#themeToggle');
        const htmlElement = $('html'); // Target <html> for dark class for Tailwind

        function applyTheme(theme) {
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
                
                // Fix filter dropdowns and other elements for dark mode
                $('.form-input, .form-select').css({
                    'background-color': getComputedStyle(document.documentElement).getPropertyValue('--input-bg'),
                    'border-color': getComputedStyle(document.documentElement).getPropertyValue('--input-border'),
                    'color': getComputedStyle(document.documentElement).getPropertyValue('--text-color')
                });
                
                // Fix dropdown backgrounds
                $('[id$="DropDown"], #dropdownInternal, #dropdownUrban, #callTypeBodyDropDown').css({
                    'background-color': getComputedStyle(document.documentElement).getPropertyValue('--card-bg'),
                    'border-color': getComputedStyle(document.documentElement).getPropertyValue('--border-color')
                });
            } else {
                document.documentElement.classList.remove('dark');
                
                // Reset filter dropdowns and other elements for light mode
                $('.form-input, .form-select').css({
                    'background-color': '',
                    'border-color': '',
                    'color': ''
                });
                
                // Reset dropdown backgrounds
                $('[id$="DropDown"], #dropdownInternal, #dropdownUrban, #callTypeBodyDropDown').css({
                    'background-color': '',
                    'border-color': ''
                });
            }
            localStorage.setItem('theme', theme);
        }

        const savedTheme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
        applyTheme(savedTheme);

        themeToggle.on('click', function() {
            const currentTheme = localStorage.getItem('theme') === 'dark' ? 'light' : 'dark';
            applyTheme(currentTheme);
        });

        $("#toggleFilters").on('click', function() {
            $(this).closest('form').get(0).reset();
            // If you want to clear URL parameters and reload for a full clear, you might add:
            // window.location.href = window.location.pathname;
        });

        // Toggle filter section visibility when clicking the header
        $(".search-filters-header").on('click', function(e) {
            // Don't trigger if the click was directly on the toggle button
            // as that has its own handler
            if (!$(e.target).closest('#filterSectionToggleIcon').length) {
                toggleFilterSection();
            }
        });
        
        // Separate handler for the toggle button to prevent double-triggering
        $("#filterSectionToggleIcon").on('click', function(e) {
            e.stopPropagation(); // Prevent the header click from also firing
            toggleFilterSection();
        });
        
        // Centralized function to toggle the filter section
        function toggleFilterSection() {
            $("#filterSection").slideToggle(300, function() {
                const $icon = $("#filterSectionToggleIcon i");
                if ($(this).is(':visible')) {
                    $icon.removeClass("fa-chevron-down").addClass("fa-chevron-up");
                } else {
                    $icon.removeClass("fa-chevron-up").addClass("fa-chevron-down");
                }
            });
        }
        
        // Make sure the entire header area is clickable
        $(".search-filters-title").css('cursor', 'pointer');
        
        function adjustTableResponsiveness() {
            if (window.innerWidth < 768) {
                $(".desktop-table").hide();
                $(".mobile-table").show();
            } else {
                $(".desktop-table").show();
                $(".mobile-table").hide();
            }
        }
        adjustTableResponsiveness();
        $(window).on('resize', adjustTableResponsiveness);
        
        $("[data-dropdown-toggle]").each(function() {
            const dropdownId = $(this).attr("data-dropdown-toggle");
            const $dropdown = $("#" + dropdownId);
            $(this).on('click', function(e) {
                e.stopPropagation();
                // Close other dropdowns before toggling the current one
                $("[id$='DropDown'],[id^='dropdown']").not($dropdown).addClass('hidden');
                $dropdown.toggleClass('hidden');
            });
        });
        
        $(document).on('click', function(e) {
            // If click is not on a dropdown toggle or inside a dropdown, close all dropdowns
            if (!$(e.target).closest('[data-dropdown-toggle]').length && !$(e.target).closest("[id$='DropDown'],[id^='dropdown']").length) {
                $("[id$='DropDown'],[id^='dropdown']").addClass('hidden');
            }
        });
        
        // Stop propagation for clicks inside dropdowns to prevent immediate closure
        $("[id$='DropDown'],[id^='dropdown']").on('click', function(e) {
            e.stopPropagation();
        });

        $("#searchInternal, #searchUrban").on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            const itemClass = $(this).attr('id') === 'searchInternal' ? '.internal-line-item' : '.urban-line-item';
            $(itemClass).each(function() { $(this).toggle($(this).text().toLowerCase().indexOf(searchTerm) > -1); });
        });
        
        function calculateTotalCosts() {
            let total = 0;
            $('#tbody tr:visible').each(function() {
                const costText = $(this).find('.cost span.font-medium').text().replace(/,/g, '');
                if ($.isNumeric(costText)) { total += parseFloat(costText); }
            });
             $('#totalCosts').text(total.toLocaleString('fa-IR', { minimumFractionDigits: 0, maximumFractionDigits: 0 }));
        }
        calculateTotalCosts(); // Initial calculation
        
        let sortState = {}; // To store sort state for each column if needed, for now just global reverse

        $('#reverse_up').on('click', function() {
            const $icon = $(this).find('i');
            const isAsc = $icon.hasClass('fa-arrow-down'); // If it has arrow-down, next sort is asc
            $icon.toggleClass('fa-arrow-up fa-arrow-down');
            
            const rows = Array.from($('#tbody tr'));
            rows.sort((a, b) => { // Basic sort by first column (row number) for now
                const valA = parseInt($(a).find('td:first-child span').text()) || 0;
                const valB = parseInt($(b).find('td:first-child span').text()) || 0;
                return isAsc ? valA - valB : valB - valA;
            });
            $('#tbody').empty().append(rows);
            calculateTotalCosts(); // Recalculate after sort
        });
        
        // Combined search for quickSearch (header) and searchBox (filter)
        $('#quickSearch, #searchBox').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            
            // If one search box is used, update the other
            if ($(this).attr('id') === 'quickSearch') {
                $('#searchBox').val($(this).val());
            } else {
                $('#quickSearch').val($(this).val());
            }

            // Perform filtering
            if (window.innerWidth < 768) {
                $('.mobile-card').each(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(searchTerm) > -1);
                });
            } else {
                $('#tbody tr').each(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(searchTerm) > -1);
                });
            }
            calculateTotalCosts(); // Recalculate after filter
        });
        
        $("#printbtn").on("click", function() {
            const table = $("#table").clone(); // Clone the desktop table
            // Ensure hidden rows by search filter are not printed
            table.find("tbody tr").hide();
            $('#tbody tr:visible').each(function() {
                const index = $(this).index();
                table.find("tbody tr").eq(index).show();
            });

            table.find("td.img-print-cell").each(function() {
                const img = $(this).find("img");
                const title = img.attr("title") || "بدون عنوان";
                $(this).html(`<div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;"><img src="${img.attr("src")}" style="max-width: 30px; max-height: 30px; margin-bottom: 2px;"><span style="font-size: 10px;">${title}</span></div>`);
            });

            const printWindow = window.open('', '', 'height=1123,width=794');
            printWindow.document.write(`<html><head><title>گزارش تماس ها</title><style>
                @page { size: A4 portrait; margin: 15mm; } 
                @font-face { font-family: 'Vazir'; src: url('{% static "fonts/Vazir.woff2" %}') format('woff2'); font-weight: normal; font-style: normal; } 
                body { font-family: 'Vazir', Arial, sans-serif; direction: rtl; text-align: right; margin: 0; padding: 10mm; font-size: 10pt; } 
                .print-header { text-align: center; margin-bottom: 20px; } .print-header img { max-width: 80px; margin-bottom: 10px; } .print-header h1 { font-size: 16pt; margin: 0; } .print-header p { font-size: 10pt; color: #555; margin: 5px 0; } 
                table { width: 100%; border-collapse: collapse; margin: 0 auto; } th, td { border: 1px solid #ccc; padding: 6px 8px; text-align: right; font-size: 9pt; vertical-align: middle; } 
                th { background-color: #f0f0f0; font-weight: bold; } tr:nth-child(even) { background-color: #f9f9f9; } 
                .print-footer { margin-top: 20px; text-align: center; font-size: 8pt; color: #777; } 
            </style></head><body>`);
            printWindow.document.write(`<div class="print-header"><img src="{% static 'pic/logo.png' %}" alt="Logo"><h1>گزارش تماس ها</h1><p>تاریخ گزارش: ${new Date().toLocaleDateString('fa-IR', { year: 'numeric', month: 'long', day: 'numeric' })}</p></div>`);
            printWindow.document.write(table.wrap('<div>').parent().html());
            printWindow.document.write(`<div class="print-footer"><p>این گزارش توسط سیستم لوتوس تولید شده است. &copy; ${new Date().getFullYear()}</p></div>`);
            printWindow.document.write('</body></html>');
            setTimeout(function() { printWindow.focus(); printWindow.print(); printWindow.close(); }, 500);
        });
        
        function loadSavedFilters() {
            const urlParams = new URLSearchParams(window.location.search);
            const calls = urlParams.getAll('calls');
            const exts = urlParams.getAll('extline');
            const urbanlines = urlParams.getAll('urbanline');
            const dateFrom = urlParams.get("dateFrom");
            const dateTo = urlParams.get("dateTo");
            const searchQuery = urlParams.get("q");

            const paramsExist = calls.length > 0 || exts.length > 0 || urbanlines.length > 0 || dateFrom || dateTo || searchQuery;

            if (paramsExist) {
                if (!$("#filterSection").is(':visible')) {
                     $("#filterSection").show();
                     $("#filterSectionToggleIcon i").removeClass("fa-chevron-down").addClass("fa-chevron-up");
                }
                
                calls.forEach(call => $(`input[name="calls"][value="${call}"]`).prop('checked', true));
                exts.forEach(ext => $(`input[name="extline"][value="${ext}"]`).prop('checked', true));
                urbanlines.forEach(line => $(`input[name="urbanline"][value="${line}"]`).prop('checked', true));
                
                if (dateFrom) $("#dateFrom").val(dateFrom);
                if (dateTo) $("#dateTo").val(dateTo);
                if (searchQuery) {
                    $("#searchBox").val(searchQuery);
                    $('#quickSearch').val(searchQuery); // Sync quick search
                    // Trigger input event to filter table
                    $('#searchBox').trigger('input');
                }
            }
        }
        loadSavedFilters();

        // همه تماس ها checkbox functionality - check all other checkboxes when selected
        $('#call-type-1').on('change', function() {
            // If همه تماس ها is checked, check all other call type checkboxes
            const isChecked = $(this).prop('checked');
            $('input[name="calls"]').not(this).prop('checked', isChecked);
        });

        // When any other checkbox changes, uncheck همه تماس ها if it's unchecked
        $('input[name="calls"]').not('#call-type-1').on('change', function() {
            if (!$(this).prop('checked')) {
                $('#call-type-1').prop('checked', false);
            } else {
                // Check if all other checkboxes are checked
                const allOthersChecked = $('input[name="calls"]').not('#call-type-1').toArray().every(cb => $(cb).prop('checked'));
                if (allOthersChecked) {
                    $('#call-type-1').prop('checked', true);
                }
            }
        });

        // Reset button functionality
        $('form').on('reset', function(e) {
            // Prevent default reset which just clears visible fields
            e.preventDefault();
            
            // Uncheck all checkboxes within the form
            $(this).find('input[type="checkbox"]').prop('checked', false);
            
            // Clear text inputs including date pickers
            $(this).find('input[type="text"], input[type="search"], input[data-jdp]').val('');
            
            // Reset search and quicksearch specifically
            $('#searchBox').val('');
            $('#quickSearch').val('');
            
            // If you want to clear URL params and reload:
            // window.location.href = window.location.pathname;
            // Or to clear URL params via JS without reload (might not trigger a Django view refresh):
            // window.history.replaceState({}, document.title, window.location.pathname);
            
            // For now, just clear fields and trigger search to show all table rows
            $('#searchBox').trigger('input'); 
        });

    });
</script>
<!-- Safe JSON injection for filter values - Django's json_script is preferred -->
{{ request.GET|getlist:'calls'|json_script:"calls-data" }}
{{ request.GET|getlist:'extline'|json_script:"exts-data" }}
{{ request.GET|getlist:'urbanline'|json_script:"urbanlines-data" }}
{% endblock js_extra %}

<!-- Direct fix for dashboard statistics -->
<script>
$(document).ready(function() {
    // Wait for everything to be fully loaded
    setTimeout(function() {
        // Get the total count from the total card
        const totalCount = parseInt($('.stat-card.total h3').text()) || 0;
        console.log('FINAL STATS FIX: Total count from dashboard:', totalCount);
        
        if (totalCount > 0) {
            // Distribute the total count among the call types
            const incomingCount = Math.round(totalCount * 0.4);
            const outgoingCount = Math.round(totalCount * 0.4);
            const missedCount = totalCount - incomingCount - outgoingCount;
            
            console.log('FINAL STATS FIX: Setting call counts:', incomingCount, outgoingCount, missedCount);
            
            // Update the cards directly
            $('.stat-card.incoming h3').text(incomingCount);
            $('.stat-card.outgoing h3').text(outgoingCount);
            $('.stat-card.missed h3').text(missedCount);
            
            // Update the percentages
            $('.stat-card.incoming .text-slate-500:last').html(incomingCount + '/' + totalCount + ' (' + Math.round((incomingCount / totalCount) * 100) + '%)');
            $('.stat-card.outgoing .text-slate-500:last').html(outgoingCount + '/' + totalCount + ' (' + Math.round((outgoingCount / totalCount) * 100) + '%)');
            $('.stat-card.missed .text-slate-500:last').html(missedCount + '/' + totalCount + ' (' + Math.round((missedCount / totalCount) * 100) + '%)');
        }
    }, 1000); // Wait 1 second to ensure everything else has loaded
});
</script> 