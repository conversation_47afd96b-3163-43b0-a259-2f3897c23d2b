<!-- Modern Top Navbar -->
<header class="modern-navbar" id="lotus-navbar">
    <div class="navbar-content">
        <!-- Logo and Navigation Section -->
        <div class="navbar-left">
            <div class="logo-container">
                <h1 class="logo-text">لوتوس</h1>
            </div>
        </div>

        <!-- Actions and User Section -->
        <div class="nav-actions">
            <!-- Theme Toggle -->
            <button id="theme-toggle" class="lotus-theme-toggle" aria-label="Toggle dark mode">
                <i class="fa-solid fa-sun"></i>
                <i class="fa-solid fa-moon" style="color: #00BCD4 !important;"></i>
            </button>
            
            <!-- Print Button -->
            <button type="button" id="printbtn" class="lotus-nav-button">
                <i class="fa-solid fa-print"></i> <span style="color: #00BCD4;">چاپ</span>
            </button>
        </div>
    </div>
</header>

<style>
    /* Self-contained styles with unique class names to avoid conflicts */
    #lotus-navbar {
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        position: sticky;
        top: 0;
        z-index: 1000;
        transition: all 0.3s ease;
        padding: 0.8rem 1.5rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }
    
    html.dark #lotus-navbar {
        background: rgba(13, 27, 42, 0.95); /* Deep Indigo with opacity */
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
    
    #lotus-navbar .navbar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 1400px;
        margin: 0 auto;
    }
    
    #lotus-navbar .logo-container {
        display: flex;
        align-items: center;
        position: relative;
        padding-right: 0.5rem;
    }
    
    #lotus-navbar .logo-container::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 0;
        height: 2px;
        background: linear-gradient(135deg, #00BCD4, #29B6F6); /* Lotus Aqua to Sky Blue */
        transition: width 0.3s ease;
    }
    
    #lotus-navbar .logo-container:hover::after {
        width: 100%;
    }
    
    #lotus-navbar .logo-text {
        font-size: 1.6rem;
        font-weight: 700;
        margin: 0;
        background: linear-gradient(135deg, #00BCD4, #536DFE); /* Lotus Aqua to Electric Violet */
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        transition: all 0.3s ease;
        letter-spacing: 0.5px;
    }
    
    html.dark #lotus-navbar .logo-text {
        background: linear-gradient(135deg, #00BCD4, #536DFE); /* Lotus Aqua to Electric Violet */
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    
    #lotus-navbar .nav-actions {
        display: flex;
        align-items: center;
        gap: 0.8rem;
    }
    
    .lotus-nav-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.6rem 1rem;
        border-radius: 8px;
        margin-left: 10px;
        margin-top: 10px;
        border: none;
        background: rgba(240, 242, 245, 0.8);
        color: #37474F; /* Dark Gray */
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }
    
    html.dark .lotus-nav-button {
        background: rgba(13, 27, 42, 0.8); /* Deep Indigo with opacity */
        color: #F5F7FA; /* White Smoke */
    }
    
    .lotus-nav-button:hover {
        background: #CFD8DC; /* Light Gray */
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    html.dark .lotus-nav-button:hover {
        background: rgba(0, 188, 212, 0.2); /* Lotus Aqua with opacity */
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
    }
    
    .lotus-nav-button:active {
        transform: translateY(0);
    }
    
    .lotus-theme-toggle {
        position: relative;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        padding: 0;
        overflow: hidden;
        margin-top: 0;
        transform: translateY(5px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        background: rgba(240, 242, 245, 0.9);
        color: #00BCD4; /* Lotus Aqua */
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        cursor: pointer;
    }
    
    html.dark .lotus-theme-toggle {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        background: rgba(13, 27, 42, 0.9); /* Deep Indigo with opacity */
    }
    
    .lotus-theme-toggle i {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.3s ease;
        font-size: 1rem;
    }
    
    .lotus-theme-toggle .fa-sun {
        opacity: 0;
        transform: translate(-50%, -50%) rotate(-180deg);
        color: #FF7043; /* Sunset Orange */
    }
    
    .lotus-theme-toggle .fa-moon {
        opacity: 1;
        transform: translate(-50%, -50%) rotate(0);
        color: #00BCD4; /* Lotus Aqua */
    }
    
    html.dark .lotus-theme-toggle .fa-sun {
        opacity: 1;
        transform: translate(-50%, -50%) rotate(0);
        color: #FF7043; /* Sunset Orange */
    }
    
    html.dark .lotus-theme-toggle .fa-moon {
        opacity: 0;
        transform: translate(-50%, -50%) rotate(180deg);
        color: #00BCD4; /* Lotus Aqua */
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        #lotus-navbar .button-text {
            display: none;
        }
        
        .lotus-nav-button {
            padding: 0.6rem;
        }
        
        #lotus-navbar {
            padding: 0.7rem 1rem;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize theme toggle button with forceful override
        const initThemeToggle = function() {
            const themeToggleBtn = document.getElementById('theme-toggle');
            
            if (!themeToggleBtn) return;
            
            // Remove any existing event listeners
            const newButton = themeToggleBtn.cloneNode(true);
            themeToggleBtn.parentNode.replaceChild(newButton, themeToggleBtn);
            
            // Get saved theme or use system preference
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            // Set initial theme
            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                document.documentElement.classList.add('dark');
                document.documentElement.classList.remove('light');
            } else {
                document.documentElement.classList.add('light');
                document.documentElement.classList.remove('dark');
            }
            
            // Add toggle event
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                if (document.documentElement.classList.contains('dark')) {
                    document.documentElement.classList.remove('dark');
                    document.documentElement.classList.add('light');
                    localStorage.setItem('theme', 'light');
                } else {
                    document.documentElement.classList.remove('light');
                    document.documentElement.classList.add('dark');
                    localStorage.setItem('theme', 'dark');
                }
            });
        };
        
        // Initialize theme toggle
        initThemeToggle();
        
        // Add a backup initialization for any race conditions
        setTimeout(initThemeToggle, 500);
        
        // Print functionality
        const printBtn = document.getElementById('printbtn');
        if (printBtn) {
            printBtn.addEventListener('click', function() {
                window.print();
            });
        }
    });
</script>
