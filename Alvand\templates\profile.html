{% extends 'base.html' %}
{% load static profileTags %}
{% load static %}
{% block head_extra %}
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- CSS files -->
    <link rel="stylesheet" href="{% static 'css/dashboard-fix.css' %}" />
    <link rel="stylesheet" href="{% static 'css/dashboard-width-fix.css' %}" />
    <link rel="stylesheet" href="{% static 'css/direct-layout-fix.css' %}" />
    <link rel="stylesheet" href="{% static 'css/dashboard-content.css' %}" />
    
    <style>
    /* Fix for theme toggle icons in navbar */
    .theme-toggle-btn i,
    .dark-mode-toggle i,
    .light-mode-toggle i,
    [data-theme-toggle] i,
    .theme-switch i,
    .navbar .fa-moon,
    .navbar .fa-sun,
    header .fa-moon,
    header .fa-sun,
    .nav .fa-moon,
    .nav .fa-sun,
    .theme-toggle-icon {
        color: #00BCD4 !important;
        font-size: 1.25rem !important;
        filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2)) !important;
        opacity: 1 !important;
        visibility: visible !important;
        transition: all 0.2s ease !important;
    }
    
    .dark .theme-toggle-btn i,
    .dark .dark-mode-toggle i,
    .dark .light-mode-toggle i,
    .dark [data-theme-toggle] i,
    .dark .theme-switch i,
    .dark .navbar .fa-moon,
    .dark .navbar .fa-sun,
    .dark header .fa-moon,
    .dark header .fa-sun,
    .dark .nav .fa-moon, 
    .dark .nav .fa-sun,
    .dark .theme-toggle-icon {
        color: #29B6F6 !important;
    }
    
    /* Root variables for consistent styling */
    :root {
        --primary-color: #00BCD4;       /* Lotus Aqua */
        --primary-light: #29B6F6;       /* Sky Blue */
        --primary-dark: #0097A7;        /* Darker Aqua */
        --secondary-color: #F5F7FA;     /* White Smoke */
        --text-color: #37474F;          /* Dark Gray */
        --light-text: #90A4AE;          /* Medium Gray */
        --border-color: #CFD8DC;        /* Light Gray */
        --sidebar-width: 135px;
        --content-padding: 1.25rem;
        --bg-color: #F5F7FA;            /* White Smoke */
        --card-bg: white;
        --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        --success-color: #26A69A;       /* Emerald Green */
        --warning-color: #FF7043;       /* Sunset Orange */
        --info-color: #536DFE;          /* Electric Violet */
    }
    
    /* Dark mode variables */
    .dark {
        --bg-color: #121212;              /* Dark Navy */
        --card-bg: #0D1B2A;               /* Deep Indigo */
        --text-color: #F5F7FA;            /* White Smoke */
        --light-text: #CFD8DC;            /* Light Gray */
        --border-color: #37474F;          /* Dark Gray */
        --secondary-color: #0D1B2A;       /* Deep Indigo */
        --card-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
    }
    
    body {
        background-color: var(--bg-color);
 
    }
    
    /* Input focus styles */
    input:focus, textarea:focus, select:focus {
        outline: none !important;
        border-color: #00BCD4 !important;
        box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.25) !important;
    }
    
    .dark input:focus, .dark textarea:focus, .dark select:focus {
        border-color: #29B6F6 !important;
        box-shadow: 0 0 0 2px rgba(41, 182, 246, 0.25) !important;
    }
    
    /* Main container */
        .profile-container {
        max-width: 1100px;
        margin: 0 auto;
        padding: 0 1rem;
    }
    
    /* Profile header */
    .profile-header {
        position: relative;
        margin-top: 1.5rem;
        margin-bottom: 3rem;
        padding: 0;
        display: flex;
        flex-direction: column;
        border-radius: 0.75rem;
        overflow: hidden;
    }
    
    .profile-cover {
        height: 130px;
        background: linear-gradient(135deg, #00BCD4, #536DFE);
        position: relative;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .status-badges {
        position: absolute;
        top: 1rem;
        right: 1rem;
        display: flex;
        gap: 0.5rem;
        z-index: 5;
    }
    
    .status-badge {
        background-color: rgba(255, 255, 255, 0.9);
        color: #37474F;
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(4px);
    }
    
    .status-badge i {
        color: #00BCD4;
    }
    
    .profile-cover::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }
    
    .profile-info {
        background-color: var(--card-bg);
        padding: 1.25rem 1.5rem 1.5rem;
        border-radius: 0 0 0.75rem 0.75rem;
        box-shadow: var(--card-shadow);
        margin-top: -1rem;
        border: 1px solid var(--border-color);
        border-top: none;
        position: relative;
        z-index: 10;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .dark .profile-info {
        background-color: #0D1B2A;
        border-color: #37474F;
    }
    
    .profile-avatar-container {
        position: relative;
        margin-bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 20;
        margin-top: -3.5rem;
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
        padding-right: 0.5rem;
    }
    
    @media (min-width: 640px) {
        .profile-avatar-container {
            position: relative;
            top: auto;
            left: auto;
            margin-bottom: 0.75rem;
        }
        
        .profile-user-info {
            text-align: center;
            align-items: center;
            margin-left: 0;
        }
    }
    
    .profile-avatar {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background: linear-gradient(135deg, #00BCD4, #29B6F6);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2.25rem;
        border: 4px solid white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        position: relative;
        margin-bottom: 5px;
        margin-right: 5px;
    }
    
    .dark .profile-avatar {
        border-color: #0D1B2A;
    }
    
    .camera-icon {
        position: absolute;
        bottom: -3px;
        right: -3px;
        background-color: white;
        color: #00BCD4;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
        cursor: pointer;
        font-size: 0.875rem;
        border: 2px solid #29B6F6;
        transition: all 0.2s ease;
        z-index: 25;
    }
    
    .dark .camera-icon {
        background-color: #0D1B2A;
        border: 2px solid #00BCD4;
        color: #00BCD4;
    }
    
    .camera-icon:hover {
        transform: scale(1.1);
        background-color: #00BCD4;
        color: white;
    }
    
    .dark .camera-icon:hover {
        background-color: #00BCD4;
        color: white;
    }
    
    .profile-user-info {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: transparent;
    }
    
    @media (min-width: 640px) {
        .profile-user-info {
            text-align: center;
            align-items: center;
        }
    }
    
    /* High visibility profile elements */
    .profile-name-container {
        background-color: rgba(0, 188, 212, 0.1);
        padding: 0.5rem 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .dark .profile-name-container {
        background-color: rgba(0, 188, 212, 0.15);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    
    .profile-name {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 0;
        text-align: center;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .profile-username {
        display: inline-block;
        background-color: rgba(0, 188, 212, 0.2);
        color: #0097A7;
        padding: 0.5rem 1.25rem;
        border-radius: 0.5rem;
        font-size: 1.125rem;
        font-weight: 600;
        margin: 0.75rem 0;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .dark .profile-username {
        background-color: rgba(0, 188, 212, 0.25);
        color: #80DEEA;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }
    
    .profile-detail {
        color: var(--text-color);
        font-size: 0.875rem;
        margin-bottom: 1rem;
        background-color: rgba(41, 182, 246, 0.1);
        padding: 0.375rem 1rem;
        border-radius: 0.375rem;
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        font-weight: 500;
    }
    
    .profile-detail i {
        color: #00BCD4;
        font-size: 0.75rem;
    }
    
    .dark .profile-detail {
        background-color: rgba(41, 182, 246, 0.2);
        color: #F5F7FA;
    }
    
    .dark .profile-detail i {
        color: #80DEEA;
    }
    
    .badges-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
        margin-bottom: 0.5rem;
    }
    
    @media (min-width: 640px) {
        .badges-container {
            justify-content: flex-start;
    }
    }
    
    .badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.625rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .badge i {
        margin-left: 0.375rem;
        font-size: 0.625rem;
    }
    
    .badge-success {
        background-color: rgba(38, 166, 154, 0.1);
        color: #26A69A;
    }
    
    .badge-warning {
        background-color: rgba(255, 112, 67, 0.1);
        color: #FF7043;
    }
    
    .badge-info {
        background-color: rgba(83, 109, 254, 0.1);
        color: #536DFE;
    }
    
    /* Cards */
    .card-container {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    @media (min-width: 768px) {
        .card-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    .info-card {
        background-color: var(--card-bg);
        border-radius: 0.75rem;
        box-shadow: var(--card-shadow);
        border: 1px solid var(--border-color);
        overflow: hidden;
    }
    
    .card-header {
        padding: 1rem 1.25rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: linear-gradient(to right, rgba(0, 188, 212, 0.05), rgba(83, 109, 254, 0.05));
    }
    
    .card-icon {
        width: 2rem;
        height: 2rem;
        border-radius: 0.5rem;
        background: linear-gradient(135deg, rgba(0, 188, 212, 0.2), rgba(41, 182, 246, 0.2));
        color: #00BCD4;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
    
    .card-title {
        flex: 1;
    }
    
    .card-title h3 {
        font-weight: 600;
        font-size: 1rem;
        color: var(--text-color);
        margin: 0;
    }
    
    .card-title p {
        font-size: 0.75rem;
        color: var(--light-text);
        margin: 0;
    }
    
    .card-body {
        padding: 1.25rem;
    }
    
    /* Form fields */
    .field-container {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    @media (min-width: 640px) {
        .field-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    .field-group {
        margin-bottom: 0.75rem;
    }
    
    .field-label {
        display: block;
        font-size: 0.75rem;
        font-weight: 500;
        color: var(--light-text);
        margin-bottom: 0.25rem;
    }
    
    .dark .field-label {
        color: #CFD8DC;
    }
    
    .field-value {
        font-size: 0.875rem;
        color: var(--text-color);
        background-color: rgba(245, 247, 250, 0.5);
        padding: 0.375rem 0.5rem;
        border: 1px solid var(--border-color);
        border-radius: 0.375rem;
        width: 100%;
        min-height: 2rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .dark .field-value {
        background-color: rgba(13, 27, 42, 0.8);
        color: #F5F7FA;
        border-color: #37474F;
    }
    
    .full-width {
        grid-column: 1 / -1;
    }
    
    .full-width .field-value {
        white-space: normal;
        min-height: 3rem;
    }
    
    /* Button styles */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.6875rem;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #00BCD4, #29B6F6);
        color: white;
        border: none;
        box-shadow: 0 2px 6px rgba(0, 188, 212, 0.3);
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #0097A7, #0288D1);
        box-shadow: 0 4px 8px rgba(0, 188, 212, 0.4);
        transform: translateY(-1px);
    }
    
    .btn-outline {
        background-color: transparent;
        border: 1px solid var(--border-color);
        color: var(--text-color);
    }
    
    .btn-outline:hover {
        background-color: var(--secondary-color);
        border-color: #00BCD4;
    }
    
    .btn i {
        margin-left: 0.375rem;
        font-size: 0.75rem;
    }
    
    /* Modal styling */
    .modal {
        position: fixed;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 50;
        backdrop-filter: blur(4px);
        transition: all 0.3s ease;
    }
    
    .modal-content {
        background-color: var(--card-bg);
        border-radius: 0.75rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        width: 100%;
        max-width: 400px;
        transform: scale(0.95);
        opacity: 0;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
        overflow: hidden;
    }
    
    .modal.open .modal-content {
        transform: scale(1);
        opacity: 1;
    }
    
    .hidden {
        display: none !important;
    }
    
    /* Verification Code input */
    .verification-input {
        letter-spacing: 0.25rem;
        font-family: monospace;
        background-color: rgba(245, 247, 250, 0.5);
        border-color: var(--border-color);
        color: var(--text-color);
        transition: all 0.2s ease;
    }
    
    .dark .verification-input {
        background-color: rgba(13, 27, 42, 0.8);
        border-color: #37474F;
        color: #F5F7FA;
    }
    
    .dark .modal-content {
        background-color: #0D1B2A;
        border-color: #37474F;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    }
    
    .dark .text-gray-600 {
        color: #CFD8DC !important;
    }
    
    .dark .text-gray-500 {
        color: #90A4AE !important;
    }
    
    /* Fix sidebar positioning */
    .sidebar {
        position: fixed !important;
        right: 0 !important;
        left: auto !important;
        width: 135px !important;
        z-index: 40 !important;
    }
    
    /* Card styling for dark mode */
    .dark .info-card {
        border-color: #37474F;
    }
    
    .dark .card-header {
        border-color: #37474F;
        background: linear-gradient(to right, rgba(0, 188, 212, 0.1), rgba(83, 109, 254, 0.1));
    }
    
    .dark .card-icon {
        background: linear-gradient(135deg, rgba(0, 188, 212, 0.25), rgba(83, 109, 254, 0.25));
        color: #80DEEA;
    }
    
    /* Dark mode status badges */
    .dark .status-badge {
        background-color: rgba(13, 27, 42, 0.8);
        color: #F5F7FA;
        border: 1px solid #37474F;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
    }
    
    .dark .status-badge i {
        color: #80DEEA;
    }
    
    /* Icon visibility fix - show moon in light mode, sun in dark mode */
    .fa-sun,
    .theme-toggle-btn .fa-sun,
    .dark-mode-toggle .fa-sun,
    [data-theme-toggle] .fa-sun,
    .theme-switch .fa-sun,
    .navbar .fa-sun,
    header .fa-sun,
    .nav .fa-sun,
    .nav-item .fa-sun,
    .navbar-nav .fa-sun,
    .navbar-item .fa-sun {
        display: none !important;
    }

    .fa-moon,
    .theme-toggle-btn .fa-moon,
    .dark-mode-toggle .fa-moon,
    [data-theme-toggle] .fa-moon,
    .theme-switch .fa-moon,
    .navbar .fa-moon,
    header .fa-moon,
    .nav .fa-moon,
    .nav-item .fa-moon,
    .navbar-nav .fa-moon,
    .navbar-item .fa-moon {
        display: inline-block !important;
        color: #00BCD4 !important;
    }

    /* Dark mode - show sun, hide moon */
    .dark .fa-moon,
    .dark .theme-toggle-btn .fa-moon,
    .dark .dark-mode-toggle .fa-moon,
    .dark [data-theme-toggle] .fa-moon,
    .dark .theme-switch .fa-moon,
    .dark .navbar .fa-moon,
    .dark header .fa-moon,
    .dark .nav .fa-moon,
    .dark .nav-item .fa-moon,
    .dark .navbar-nav .fa-moon,
    .dark .navbar-item .fa-moon {
        display: none !important;
    }

    .dark .fa-sun,
    .dark .theme-toggle-btn .fa-sun,
    .dark .dark-mode-toggle .fa-sun,
    .dark [data-theme-toggle] .fa-sun,
    .dark .theme-switch .fa-sun,
    .dark .navbar .fa-sun,
    .dark header .fa-sun,
    .dark .nav .fa-sun,
    .dark .nav-item .fa-sun,
    .dark .navbar-nav .fa-sun,
    .dark .navbar-item .fa-sun {
        display: inline-block !important;
        color: #80DEEA !important;
    }
    </style>
{% endblock %}

{% block main %}
<div class="profile-container">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="profile-cover">
            <div class="status-badges">
                <div class="status-badge">
                    <i class="fa-solid fa-user-tie"></i>
                    {{ user.group|getGroupnameById }}
                </div>
                
                {% if user.active %}
                    <div class="status-badge">
                        <i class="fa-solid fa-check-circle"></i>
                        حساب فعال
                    </div>
                {% else %}
                    <div class="status-badge">
                        <i class="fa-solid fa-exclamation-circle"></i>
                        حساب غیرفعال
                    </div>
                {% endif %}
                
                {% if user.email_verified_at %}
                    <div class="status-badge">
                        <i class="fa-solid fa-envelope-circle-check"></i>
                        ایمیل تایید شده
                    </div>
                {% else %}
                    <div class="status-badge">
                        <i class="fa-solid fa-envelope"></i>
                        ایمیل تایید نشده
                    </div>
                {% endif %}
            </div>
        </div>
        
        <div class="profile-avatar-container">
            <div class="profile-avatar">
                {% if user.profile_picture %}
                <img src="{{ MEDIA_URL }}{{ user.profile_picture }}?t={{ user.updated_at|date:'U' }}" alt="{{ user.name }} {{ user.lastname }}" class="w-full h-full object-cover">
                {% else %}
                <i class="fa-solid fa-user"></i>
                {% endif %}
                <div class="camera-icon">
                    <i class="fa-solid fa-camera"></i>
                </div>
            </div>
        </div>
        
        <div class="profile-info">
            <div class="profile-user-info">
                <div class="profile-name-container">
                    <h1 class="profile-name">{{ user.name|capfirst }} {{ user.lastname|capfirst }}</h1>
                </div>
                <div class="profile-username">{{ user.username }}</div>
                <p class="profile-detail">
                    <i class="fa-solid fa-location-dot"></i>
                    {{ user.username|getFieldOfInfo:"city" }}، {{ user.username|getFieldOfInfo:"province"|getValueOfIndexes:"province" }}
                </p>
            </div>
        </div>
    </div>
    
    <!-- Profile Content -->
    <div class="card-container">
            <!-- Personal Information -->
        <div class="info-card">
            <div class="card-header">
                <div class="card-icon">
                            <i class="fa-solid fa-user"></i>
                    </div>
                <div class="card-title">
                    <h3>اطلاعات شخصی</h3>
                    <p>اطلاعات اصلی کاربر</p>
                    </div>
                </div>
                
            <div class="card-body">
                <div class="field-container">
                    <div class="field-group">
                        <label class="field-label">نام کاربری</label>
                        <div class="field-value">{{ user.username|capfirst }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">نام</label>
                        <div class="field-value">{{ user.name|capfirst }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">نام خانوادگی</label>
                        <div class="field-value">{{ user.lastname|capfirst }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">کد ملی</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"nationalcode" }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">جنسیت</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"gender"|getValueOfIndexes:"gender" }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">تاریخ تولد</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"birthdate" }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">وضعیت تاهل</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"maritalstatus"|getValueOfIndexes:"marital" }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">وضعیت سربازی</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"military"|getValueOfIndexes:"military" }}</div>
                    </div>
                </div>
                    </div>
                </div>

            <!-- Contact Information -->
        <div class="info-card">
            <div class="card-header">
                <div class="card-icon">
                            <i class="fa-solid fa-address-book"></i>
                    </div>
                <div class="card-title">
                    <h3>اطلاعات تماس</h3>
                    <p>راه‌های ارتباطی با کاربر</p>
                    </div>
                </div>
                
            <div class="card-body">
                <div class="field-container">
                    <div class="field-group">
                        <label class="field-label">ایمیل</label>
                        <div class="field-value" dir="ltr" style="display: flex; justify-content: space-between; align-items: center">
                            <span>{{ user.email|capfirst }}</span>
                                {% if not user.email_verified_at %}
                                    {% if not email %}
                                    <form method="post" class="inline">
                                        {% csrf_token %}
                                    <button type="submit" name="verifyEmail" class="btn btn-primary btn-sm">
                                        <i class="fa-solid fa-envelope-circle-check"></i>
                                        تایید
                                    </button>
                                </form>
                                {% else %}
                                <button id="verify" class="btn btn-primary btn-sm">
                                    <i class="fa-solid fa-envelope-circle-check"></i>
                                    تایید
                                </button>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">شماره همراه</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"phonenumber" }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">تلفن ثابت</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"telephone" }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">داخلی</label>
                        <div class="field-value">{{ user.extension }}</div>
                    </div>
                    
                    <div class="field-group full-width">
                        <label class="field-label">آدرس</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"address" }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">استان</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"province"|getValueOfIndexes:"province" }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">شهر</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"city" }}</div>
                    </div>
                    </div>
                </div>
            </div>

            <!-- Professional Information -->
        <div class="info-card">
            <div class="card-header">
                <div class="card-icon">
                            <i class="fa-solid fa-briefcase"></i>
                    </div>
                <div class="card-title">
                    <h3>اطلاعات شغلی</h3>
                    <p>جزئیات سازمانی و تخصصی کاربر</p>
                    </div>
                </div>
                
            <div class="card-body">
                <div class="field-container">
                    <div class="field-group">
                        <label class="field-label">نقش سازمانی</label>
                        <div class="field-value">{{ user.group|getGroupnameById }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">مدرک تحصیلی</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"educationdegree"|getValueOfIndexes:"degree" }}</div>
                    </div>
                    
                    <div class="field-group full-width">
                        <label class="field-label">رشته تحصیلی</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"educationfield" }}</div>
                    </div>
                    </div>
                </div>
            </div>

            <!-- Banking Information -->
        <div class="info-card">
            <div class="card-header">
                <div class="card-icon">
                            <i class="fa-solid fa-credit-card"></i>
                    </div>
                <div class="card-title">
                    <h3>اطلاعات بانکی</h3>
                    <p>اطلاعات حساب و کارت بانکی</p>
                    </div>
                </div>
                
            <div class="card-body">
                <div class="field-container">
                    <div class="field-group">
                        <label class="field-label">شماره کارت</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"cardnumber" }}</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">شماره حساب</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"accountnumber" }}</div>
                    </div>
                    
                    <div class="field-group full-width">
                        <label class="field-label">شماره شبا</label>
                        <div class="field-value">{{ user.username|getFieldOfInfo:"accountnumbershaba" }}</div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
{% if email %}
<div id="emailMod" class="modal {% if not email %}hidden{% endif %}">
    <div class="modal-content max-w-md w-full p-5">
        <div class="text-center">
            <div class="w-14 h-14 rounded-full flex items-center justify-center mx-auto mb-4" style="background: linear-gradient(135deg, rgba(0, 188, 212, 0.15), rgba(83, 109, 254, 0.15)); color: #00BCD4; box-shadow: 0 4px 12px rgba(0, 188, 212, 0.2);">
                <i class="fa-solid fa-envelope text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold mb-3">تایید آدرس ایمیل</h3>
            <p class="mb-4 text-sm text-gray-600">لطفا کد ارسال شده به ایمیل خود را وارد کنید</p>
            
            <form method="POST" class="space-y-3">
                {% csrf_token %}
                <div class="mb-4">
                    <input type="number" name="code" 
                           class="verification-input w-full text-center text-xl py-2.5 px-3 border rounded-lg transition duration-200"
                           placeholder="- - - - - -" 
                           maxlength="6">
                </div>
                
                <div class="flex justify-between items-center text-xs mb-3">
                    <span class="text-gray-500">کد را دریافت نکردید؟</span>
                    <button type="submit" name="resend" style="color: #00BCD4; font-weight: 500; transition: all 0.2s ease;">
                        ارسال مجدد
                    </button>
                </div>
                
                <div class="flex gap-2 mt-4">
                    <button type="button" id="cancel" class="btn btn-outline flex-1 text-sm">
                        انصراف
                    </button>
                    <button type="submit" name="verify" class="btn btn-primary flex-1 text-sm">
                        تأیید
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const emailModal = document.getElementById('emailMod');
        
        if (emailModal) {
            // Cancel button functionality
            document.getElementById('cancel').addEventListener('click', function() {
                emailModal.classList.remove('open');
                setTimeout(() => {
                emailModal.classList.add('hidden');
                }, 200);
            });
            
            // Verify button functionality (if exists)
            const verifyBtn = document.getElementById('verify');
            if (verifyBtn) {
                verifyBtn.addEventListener('click', function() {
                    emailModal.classList.remove('hidden');
                    setTimeout(() => {
                        emailModal.classList.add('open');
                    }, 10);
                });
            }
            
            // Animation for modal
            if (!emailModal.classList.contains('hidden')) {
                setTimeout(() => {
                    emailModal.classList.add('open');
                }, 10);
            }
            
            // Close when clicking outside
            emailModal.addEventListener('click', function(e) {
                if (e.target === emailModal) {
                    emailModal.classList.remove('open');
                    setTimeout(() => {
                        emailModal.classList.add('hidden');
                    }, 200);
                }
            });
        }
    });
</script>
{% endif %}

<!-- Profile Picture Upload Modal -->
<div id="profilePicModal" class="modal hidden">
    <div class="modal-content max-w-md w-full p-5">
        <div class="text-center">
            <div class="w-14 h-14 rounded-full flex items-center justify-center mx-auto mb-4" style="background: linear-gradient(135deg, rgba(0, 188, 212, 0.15), rgba(83, 109, 254, 0.15)); color: #00BCD4; box-shadow: 0 4px 12px rgba(0, 188, 212, 0.2);">
                <i class="fa-solid fa-image text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold mb-3">بارگذاری تصویر پروفایل</h3>
            <p class="mb-4 text-sm text-gray-600">تصویر مورد نظر خود را انتخاب کنید</p>
            
            <form method="POST" enctype="multipart/form-data" class="space-y-3" id="profilePicForm">
                {% csrf_token %}
                <div class="mb-4">
                    <div class="relative border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-primary-500 transition-all" id="dropArea">
                        <input type="file" name="profile_picture" id="profilePicInput" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer" accept="image/*">
                        <div class="space-y-2" id="uploadPrompt">
                            <i class="fa-solid fa-cloud-arrow-up text-2xl" style="color: #00BCD4;"></i>
                            <p class="text-sm">برای انتخاب تصویر کلیک کنید یا فایل را اینجا رها کنید</p>
                            <p class="text-xs text-gray-500">حداکثر اندازه: 2MB</p>
                        </div>
                        <div class="hidden space-y-2" id="previewContainer">
                            <img src="#" alt="پیش‌نمایش" class="max-h-48 mx-auto rounded-lg" id="imagePreview">
                            <p class="text-sm" id="fileName"></p>
                        </div>
                    </div>
                </div>
                
                <div class="flex gap-2 mt-4">
                    <button type="button" id="cancelUpload" class="btn btn-outline flex-1 text-sm">
                        انصراف
                    </button>
                    <button type="submit" name="uploadProfilePic" class="btn btn-primary flex-1 text-sm">
                        ذخیره تصویر
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Profile Picture Modal
        const profilePicModal = document.getElementById('profilePicModal');
        const cameraIcon = document.querySelector('.camera-icon');
        const cancelUploadBtn = document.getElementById('cancelUpload');
        const profilePicInput = document.getElementById('profilePicInput');
        const imagePreview = document.getElementById('imagePreview');
        const fileName = document.getElementById('fileName');
        const uploadPrompt = document.getElementById('uploadPrompt');
        const previewContainer = document.getElementById('previewContainer');
        const profilePicForm = document.getElementById('profilePicForm');
        
        // Open modal when camera icon is clicked
        if (cameraIcon) {
            cameraIcon.addEventListener('click', function() {
                profilePicModal.classList.remove('hidden');
                setTimeout(() => {
                    profilePicModal.classList.add('open');
                }, 10);
            });
        }
        
        // Close modal when cancel button is clicked
        if (cancelUploadBtn) {
            cancelUploadBtn.addEventListener('click', function() {
                profilePicModal.classList.remove('open');
                setTimeout(() => {
                    profilePicModal.classList.add('hidden');
                    // Reset form
                    profilePicForm.reset();
                    uploadPrompt.classList.remove('hidden');
                    previewContainer.classList.add('hidden');
                }, 200);
            });
        }
        
        // Close when clicking outside
        if (profilePicModal) {
            profilePicModal.addEventListener('click', function(e) {
                if (e.target === profilePicModal) {
                    profilePicModal.classList.remove('open');
                    setTimeout(() => {
                        profilePicModal.classList.add('hidden');
                        // Reset form
                        profilePicForm.reset();
                        uploadPrompt.classList.remove('hidden');
                        previewContainer.classList.add('hidden');
                    }, 200);
                }
            });
        }
        
        // File input change handler
        if (profilePicInput) {
            profilePicInput.addEventListener('change', handleFileSelect);
        }
        
        // Drag and drop handlers
        if (dropArea) {
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });
            
            function highlight() {
                dropArea.classList.add('border-primary-500');
                dropArea.style.backgroundColor = 'rgba(0, 188, 212, 0.05)';
            }
            
            function unhighlight() {
                dropArea.classList.remove('border-primary-500');
                dropArea.style.backgroundColor = '';
            }
            
            dropArea.addEventListener('drop', handleDrop, false);
        }
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            if (files.length) {
                profilePicInput.files = files;
                handleFileSelect();
            }
        }
        
        function handleFileSelect() {
            const file = profilePicInput.files[0];
            
            if (!file) return;
            
            // Validate file type
            const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!validTypes.includes(file.type)) {
                alert('لطفا یک فایل تصویری معتبر انتخاب کنید (JPG, PNG, GIF, WEBP)');
                profilePicForm.reset();
                return;
            }
            
            // Validate file size (max 2MB)
            if (file.size > 2 * 1024 * 1024) {
                alert('حجم فایل باید کمتر از 2 مگابایت باشد');
                profilePicForm.reset();
                return;
            }
            
            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                fileName.textContent = file.name;
                uploadPrompt.classList.add('hidden');
                previewContainer.classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }
        
        // Form submission
        if (profilePicForm) {
            profilePicForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(profilePicForm);
                formData.append('action', 'upload_profile_pic');
                
                // Show loading state
                const submitBtn = profilePicForm.querySelector('button[type="submit"]');
                const originalBtnText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> در حال بارگذاری...';
                submitBtn.disabled = true;
                
                fetch(window.location.href, {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update profile picture in UI
                        const profileAvatar = document.querySelector('.profile-avatar');
                        if (profileAvatar) {
                            // Remove the icon if it exists
                            const userIcon = profileAvatar.querySelector('.fa-user');
                            if (userIcon) {
                                userIcon.remove();
                            }
                            
                            // Check if there's already an image
                            let avatarImg = profileAvatar.querySelector('img');
                            if (!avatarImg) {
                                avatarImg = document.createElement('img');
                                avatarImg.className = 'w-full h-full object-cover';
                                profileAvatar.appendChild(avatarImg);
                            }
                            
                            // Set the new image with a cache-busting parameter
                            avatarImg.src = data.image_url + '?t=' + new Date().getTime();
                        }
                        
                        // Close modal
                        profilePicModal.classList.remove('open');
                        setTimeout(() => {
                            profilePicModal.classList.add('hidden');
                            // Reset form
                            profilePicForm.reset();
                            uploadPrompt.classList.remove('hidden');
                            previewContainer.classList.add('hidden');
                            
                            // Show success message
                            alert('تصویر پروفایل با موفقیت بارگذاری شد');
                        }, 200);
                    } else {
                        alert(data.error || 'خطا در بارگذاری تصویر');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطا در ارتباط با سرور');
                })
                .finally(() => {
                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                });
            });
        }
    });
</script>
{% endblock %}