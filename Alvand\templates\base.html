{% load static baseTags %}
<!DOCTYPE html>
<html lang="fa" dir="rtl" class="light">
<head>
    <title>لوتوس - {{ pageTitle }}</title>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, shrink-to-fit=no, maximum-scale=1, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <script src="{% static 'dist/jalalidatepicker.js' %}"></script>
    <link rel="stylesheet" href="{% static 'dist/jalalidatepicker.css' %}"/>
    <link href="{% static 'fontawesomefree/css/fontawesome.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'fontawesomefree/css/brands.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'fontawesomefree/css/solid.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'fonts/css/iransans.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/dist/styles.css' %}" rel="stylesheet" type="text/css">
    <!-- Theme CSS with variables matching login page -->
    <link href="{% static 'css/theme.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/mobile-responsive-fix.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/dashboard-fix.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/mobile-menu-fix.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/sidebar-dark-mode.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/dark-mode-fix.css' %}" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="{% static 'css/high-contrast-dark.css' %}">
    <link href="{% static 'css/sidebar-desktop-fix.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/mobile-sidebar-fix.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/mobile-links-fix.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/sidebar-padding-fix.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'css/search-filters-redesign.css' %}" rel="stylesheet" type="text/css">
  
  
    {% block head_extra %}{% endblock %}
</head>

<body>
<div class="relative">
    <!-- Theme Toggle functionality preserved in JavaScript only -->

    {% include 'includes/sidebar.html' %}
    
    <div class="main-content ml-0 lg:ml-[135px]">
        {% include 'includes/navbar.html' %}
        
        <div class="content-wrapper px-4 py-6">
            {% block main %}{% endblock %}
        </div>
    </div>
</div>

<script src="{% static 'js/jquery.js' %}"></script>
<script>
    // Theme toggle functionality
    $(document).ready(function() {
        // Get the theme toggle button
        const themeToggle = $("#theme-toggle");
        
        // Check for saved theme preference or use default
        const savedTheme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
        document.documentElement.className = savedTheme;
        
        // Apply all theme-related styles based on the current theme
        function applyTheme(theme) {
            if (theme === 'dark') {
                document.documentElement.classList.remove('light');
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
                document.documentElement.classList.add('light');
            }
            localStorage.setItem('theme', theme);
        }
        
        // Apply saved theme on page load
        applyTheme(savedTheme);
        
        // Listen for clicks on the theme toggle
        themeToggle.on('click', function() {
            // If current theme is light, switch to dark, and vice versa
            const newTheme = document.documentElement.classList.contains('dark') ? 'light' : 'dark';
            applyTheme(newTheme);
        });
    });

    $(document).ready(function () {
        const show = $('#dropdownmenu')
        const show_mb = $('#dropdownmenu-mb')
        $('#show-settings').on('click', function () {
            show.toggleClass('hidden')
        });

        $('#show-settings-mb').on('click', function () {
            show_mb.toggleClass('hidden')
        })
        $(document).on('click', function (e) {
            if (!show.hasClass('hidden') && !$(e.target).closest('#show-settings, #dropdownmenu').length) {
                show.addClass('hidden');
            }
            if (!show_mb.hasClass('hidden') && !$(e.target).closest('#show-settings-mb, #dropdownmenu-mb').length) {
                show_mb.addClass('hidden');
            }
        });
    });

    $(document).ready(function () {
        const sidebar = $("#mobile-sidebar");
        const toggleButton = $("#toggle-menu");
        const menuIcon = $("#menu-icon");
        let isSidebarOpen = false;

        toggleButton.on('click', function () {
            isSidebarOpen = !isSidebarOpen;

            if (isSidebarOpen) {
                sidebar.css("transform", "translateX(0)");
                $("body").addClass("overflow-hidden"); // Prevent scrolling when sidebar is open
            } else {
                sidebar.css("transform", "translateX(100%)");
                $("body").removeClass("overflow-hidden"); // Allow scrolling when sidebar is closed
            }

            menuIcon.toggleClass("fa-bars fa-times");
            toggleButton.toggleClass("right-4 right-32");
        });

        // Close sidebar when clicking outside
        $(document).on('click', function(e) {
            if (isSidebarOpen && !sidebar.is(e.target) && sidebar.has(e.target).length === 0 && !toggleButton.is(e.target)) {
                isSidebarOpen = false;
                sidebar.css("transform", "translateX(100%)");
                $("body").removeClass("overflow-hidden");
                menuIcon.removeClass("fa-times").addClass("fa-bars");
                toggleButton.removeClass("right-32").addClass("right-4");
            }
        });
    });

    $(document).ready(function () {
        const exit = $('#exit')
        const exitmodal = $('#exitmodal')
        const exit_mb = $('#exit-mb')
        const exitmodal_mb = $('#exitmodal-mb')
        exit.on('click', function () {
            exitmodal.toggleClass("hidden")
        })
        exit_mb.on('click', function () {
            exitmodal_mb.toggleClass("hidden")
        })
        $('#close').on('click', function () {
            exitmodal.toggleClass("hidden")
        })
        $('#close-mb').on('click', function () {
            exitmodal_mb.toggleClass("hidden")
        })
    })
</script>
<script>
    jalaliDatepicker.startWatch({
        minDate: "attr",
        maxDate: "today",
        hideAfterChange: true,
        autoHide: true,
        showTodayBtn: true,
        showEmptyBtn: true
    });
</script>
<script src="{% static 'js/remove-theme-toggle.js' %}"></script>
<script src="{% static 'js/hamburger-fix.js' %}"></script>
<script src="{% static 'js/mobile-toggle-fix.js' %}"></script>
<script src="{% static 'js/mobile-sidebar-fix.js' %}"></script>
<script src="{% static 'js/icon-state-fix.js' %}"></script>
{% block js_extra %}{% endblock %}
</body>
</html>
