# Generated by Django 5.1.6 on 2025-03-01 14:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Alvand', '0008_faults_date_time_alter_faults_created_at'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='users',
            name='internal',
        ),
        migrations.AlterField(
            model_name='infos',
            name='educationdegree',
            field=models.CharField(blank=True, choices=[('0', 'زیر دیپلم'), ('1', 'دیپلم'), ('2', 'فوق دیپلم'), ('3', 'لیسانس '), ('4', 'فوق لیسانس'), ('5', 'دکترا'), ('6', 'فوق دکترا')], max_length=191, null=True, verbose_name='مدرک:'),
        ),
        migrations.AlterField(
            model_name='infos',
            name='province',
            field=models.CharField(blank=True, choices=[('0', 'آذربایجان شرقی'), ('1', 'آذربایجان غربی'), ('2', '\tاردبیل'), ('3', 'اصفهان'), ('4', 'البرز'), ('5', 'ایلام'), ('6', 'بوشهر'), ('7', '\tتهران'), ('8', 'چهارمحال و بختیاری'), ('9', 'خراسان جنوبی'), ('10', 'خراسان رضوی'), ('11', 'خراسان شمالی'), ('12', 'خوزستان'), ('13', 'زنجان'), ('14', 'سمنان'), ('15', 'سیستان و بلوچستان'), ('16', 'فارس'), ('17', 'قزوین'), ('18', 'قم'), ('19', 'کردستان'), ('20', 'کرمان'), ('21', 'کرمانشاه'), ('22', 'کهگیلویه و بویراحمد'), ('23', 'گلستان'), ('24', 'گیلان'), ('25', 'لرستان'), ('26', 'مازندران'), ('27', 'مرکزی'), ('28', 'هرمزگان'), ('29', 'همدان'), ('30', 'یزد')], max_length=191, null=True, verbose_name='استان:'),
        ),
    ]
