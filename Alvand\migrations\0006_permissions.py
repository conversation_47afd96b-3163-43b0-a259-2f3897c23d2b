# Generated by Django 5.1.6 on 2025-02-26 09:49

import django.contrib.postgres.fields
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Alvand', '0005_delete_permissions'),
    ]

    operations = [
        migrations.CreateModel(
            name='Permissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('perm_email', models.BooleanField(blank=True, null=True)),
                ('can_view', models.BooleanField(default=False, verbose_name='مجوز دیدن')),
                ('can_write', models.BooleanField(default=False, verbose_name='مجوز ایجاد کردن')),
                ('can_delete', models.BooleanField(default=False, verbose_name='مجوز حذف کردن')),
                ('can_modify', models.BooleanField(default=False, verbose_name='مجوز ویرایش کردن')),
                ('exts_label', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(), blank=True, null=True, size=None)),
                ('errorsreport', models.BooleanField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(db_column='user', on_delete=django.db.models.deletion.CASCADE, to='Alvand.users')),
            ],
        ),
    ]
