# Generated by Django 5.1.6 on 2025-03-02 06:07

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('Alvand', '0011_device'),
    ]

    operations = [
        migrations.DeleteModel(
            name='Connections',
        ),
        migrations.DeleteModel(
            name='Costs',
        ),
        migrations.DeleteModel(
            name='Countries',
        ),
        migrations.DeleteModel(
            name='Device',
        ),
        migrations.RemoveField(
            model_name='emailsending',
            name='byadmin',
        ),
        migrations.DeleteModel(
            name='Errors',
        ),
        migrations.RemoveField(
            model_name='extensionsgroups',
            name='modifyby',
        ),
        migrations.DeleteModel(
            name='Faults',
        ),
        migrations.AlterUniqueTogether(
            name='groups',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='users',
            name='group',
        ),
        migrations.RemoveField(
            model_name='infos',
            name='user',
        ),
        migrations.RemoveField(
            model_name='log',
            name='user',
        ),
        migrations.RemoveField(
            model_name='permissions',
            name='user',
        ),
        migrations.DeleteModel(
            name='Records',
        ),
        migrations.DeleteModel(
            name='Telephons',
        ),
        migrations.RemoveField(
            model_name='verifications',
            name='user',
        ),
        migrations.DeleteModel(
            name='Emailsending',
        ),
        migrations.DeleteModel(
            name='Extensionsgroups',
        ),
        migrations.DeleteModel(
            name='Groups',
        ),
        migrations.DeleteModel(
            name='Infos',
        ),
        migrations.DeleteModel(
            name='Log',
        ),
        migrations.DeleteModel(
            name='Permissions',
        ),
        migrations.DeleteModel(
            name='Users',
        ),
        migrations.DeleteModel(
            name='Verifications',
        ),
    ]
