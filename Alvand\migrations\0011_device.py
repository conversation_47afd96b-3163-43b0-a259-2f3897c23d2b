# Generated by Django 5.1.6 on 2025-03-01 14:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Alvand', '0010_delete_device'),
    ]

    operations = [
        migrations.CreateModel(
            name='Device',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stopbits', models.FloatField(blank=True, null=True)),
                ('baudrate', models.BigIntegerField(blank=True, null=True)),
                ('parity', models.TextField(blank=True, null=True)),
                ('databits', models.IntegerField(blank=True, null=True)),
                ('flow', models.TextField(blank=True, null=True)),
                ('smdrip', models.TextField(blank=True, null=True)),
                ('smdrport', models.IntegerField(blank=True, null=True)),
                ('smdrpassword', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, null=True)),
            ],
        ),
    ]
