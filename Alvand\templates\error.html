{% extends 'base.html' %}
{% load static %}
{% load errortags %}

{% block head_extra %}
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="{% static 'css/dashboard-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/dashboard-width-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/direct-layout-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/dashboard-content.css' %}">


<style>
    :root {
        /* Match theme.css variables */
        --primary-color: #00BCD4;       /* Lotus Aqua */
        --primary-hover: #29B6F6;       /* Sky Blue */
        --secondary-color: #F5F7FA;     /* White Smoke */
        --text-color: #37474F;          /* Dark Gray */
        --light-text: #90A4AE;          /* Medium Gray */
        --border-color: #CFD8DC;        /* Light Gray */
        --bg-color: #F5F7FA;            /* White Smoke */
        --card-bg: white;
        --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        
        /* Dashboard-specific variables */
        --primary: var(--primary-color);
        --primary-dark: #0097A7;        /* Darker Aqua */
        --primary-light: #80DEEA;       /* Soft Mint */
        --secondary: #26A69A;           /* Emerald Green */
        --warning: #FF7043;             /* Sunset Orange */
        --danger: #FF5252;              /* Bright Red */
        --light: #F5F7FA;               /* White Smoke */
        --dark: #0D1B2A;                /* Deep Indigo */
        --gray: #90A4AE;                /* Medium Gray */
        --gray-light: #CFD8DC;          /* Light Gray */
        --transition: all 0.3s ease;
    }
    
    /* Dark mode variables to match theme.css */
    html.dark {
        --primary-color: #00BCD4;         /* Lotus Aqua */
        --primary-hover: #29B6F6;         /* Sky Blue */
        --secondary-color: #0D1B2A;       /* Deep Indigo */
        --text-color: #F5F7FA;            /* White Smoke */
        --light-text: #CFD8DC;            /* Light Gray */
        --border-color: #37474F;          /* Dark Gray */
        --bg-color: #121212;              /* Dark Navy */
        --card-bg: #0D1B2A;               /* Deep Indigo */
        --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        
        /* Update dashboard-specific dark mode variables */
        --primary: var(--primary-color);
        --primary-dark: var(--primary-hover);
        --light: var(--text-color);
        --dark: var(--bg-color);
        --gray: var(--light-text);
        --gray-light: var(--border-color);
    }
    
    /* Fix for theme toggle icons in navbar */
    .theme-toggle-btn i,
    .dark-mode-toggle i,
    .light-mode-toggle i,
    [data-theme-toggle] i,
    .theme-switch i,
    #themeToggle i,
    .navbar .fa-moon,
    .navbar .fa-sun,
    header .fa-moon,
    header .fa-sun,
    .nav .fa-moon,
    .nav .fa-sun,
    .theme-toggle-icon {
        color: #00BCD4 !important;     /* Lotus Aqua */
        font-size: 1.25rem !important;
        filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2)) !important;
        opacity: 1 !important;
        visibility: visible !important;
        transition: all 0.2s ease !important;
    }
    
    .dark .theme-toggle-btn i,
    .dark .dark-mode-toggle i,
    .dark .light-mode-toggle i,
    .dark [data-theme-toggle] i,
    .dark .theme-switch i,
    .dark #themeToggle i,
    .dark .navbar .fa-moon,
    .dark .navbar .fa-sun,
    .dark header .fa-moon,
    .dark header .fa-sun,
    .dark .nav .fa-moon, 
    .dark .nav .fa-sun,
    .dark .theme-toggle-icon {
        color: #80DEEA !important;    /* Soft Mint */
    }
    
    /* Specific styles for sun/moon icons to ensure visibility */
    .fa-moon {
        color: #00BCD4 !important;    /* Lotus Aqua */
    }
    
    .dark .fa-moon {
        color: #80DEEA !important;    /* Soft Mint */
    }
    
    .fa-sun {
        color: #FF7043 !important;    /* Sunset Orange */
    }
    
    .dark .fa-sun {
        color: #FF9E80 !important;    /* Lighter Sunset Orange */
    }
    
    /* Show correct icon based on theme */
    .fa-sun {
        display: none !important;
    }
    
    .fa-moon {
        display: inline-block !important;
    }
    
    .dark .fa-sun {
        display: inline-block !important;
    }
    
    .dark .fa-moon {
        display: none !important;
    }
    
    /* Base RTL Layout */
    body {
        direction: rtl;
        text-align: right;
        font-family: 'Vazir', system-ui, -apple-system, sans-serif;
        color: var(--text-color);
        background-color: var(--bg-color);
        min-height: 100vh;
        line-height: 1.6;
        transition: background-color 0.3s, color 0.3s;
    }
    
    @media (max-width: 768px) {
        .main-content {
            margin-right: 0;
            margin-left: 0;
            width: 100%;
        }
    }

    .filter-section {
        background: white;
        border-radius: 0.75rem;
        padding: 1.25rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-weight: 500;
        transition: all 0.2s;
    }

    .btn-primary {
        background-color: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background-color: var(--primary-hover);
    }

    .btn-secondary {
        background-color: var(--secondary-color);
        color: white;
    }

    .btn-secondary:hover {
        background-color: var(--secondary-hover);
    }

    .input-field {
        border: 1px solid var(--border-color);
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        transition: all 0.2s;
    }

    .input-field:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.1); /* Lotus Aqua with opacity */
        outline: none;
    }

    .table-container {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow-x: auto;
    }

    .table-modern {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        min-width: 650px;
    }

    .table-modern th {
        background-color: #f8fafc;
        padding: 1rem;
        font-weight: 600;
        text-align: center;
        border-bottom: 2px solid var(--border-color);
    }

    .table-modern td {
        padding: 1rem;
        text-align: center;
        border-bottom: 1px solid var(--border-color);
    }

    .table-modern tbody tr:hover {
        background-color: #f8fafc;
    }

    .sort-btn {
        background: transparent;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.375rem;
        transition: all 0.2s;
    }

    .sort-btn:hover {
        background-color: #f1f5f9;
    }

    .pagination {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .pagination-btn {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-weight: 500;
        transition: all 0.2s;
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    @media print {
        .filter-section,
        .sort-btn,
        .pagination {
            display: none !important;
        }

        .table-container {
            box-shadow: none;
            border: none;
        }

        .table-modern th,
        .table-modern td {
            border: 1px solid #000;
        }
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
        .filter-section {
            padding: 1rem 0.75rem;
        }
        
        .flex.flex-wrap.gap-4 {
            gap: 0.75rem !important;
        }
        
        .flex.items-center.gap-3 {
            flex-direction: column;
            align-items: flex-start;
            width: 100%;
            gap: 0.5rem !important;
        }
        
        form.flex.items-center.gap-3 {
            flex-direction: column;
            width: 100%;
        }
        
        form .flex.items-center.gap-3 {
            flex-direction: row;
            flex-wrap: wrap;
            width: 100%;
        }
        
        .input-field {
            width: 100%;
            max-width: 100%;
        }
        
        input[type="search"].input-field.w-64 {
            width: 100%;
        }
        
        .pagination {
            width: 100%;
            justify-content: space-between;
        }
        
        .btn {
            width: 100%;
            text-align: center;
        }
        
        .table-modern th,
        .table-modern td {
            padding: 0.75rem 0.5rem;
            font-size: 0.875rem;
        }
        
        /* Make the most important columns visible by default */
        .table-modern th:nth-child(n+5),
        .table-modern td:nth-child(n+5) {
            display: table-cell;
        }
        
        .table-modern th:nth-child(-n+4),
        .table-modern td:nth-child(-n+4) {
            display: none;
        }
        
        /* Show the row number column always */
        .table-modern th:last-child,
        .table-modern td:last-child {
            display: table-cell;
        }
    }
    
    /* For even smaller screens */
    @media (max-width: 480px) {
        .filter-section {
            padding: 0.75rem 0.5rem;
        }
        
        .pagination span {
            display: none;
        }
    }
</style>
{% endblock %}

{% block main %}

        <div class="my-0 w-full">
            <div class="w-full h-auto mt-1 mx-auto flex flex-col rtl">
                <div class="filter-section">
                    <div class="flex flex-wrap gap-4 justify-items-center items-center">
                        <div class="flex items-center gap-3">
                            <form method="get" class="flex items-center gap-3">
                                <div class="flex items-center gap-3">
                                    <input 
                                        id="dateFrom" 
                                        data-jdp 
                                        placeholder="از تاریخ" 
                                        name="dateFrom" 
                                        class="input-field"
                                        onchange="labelInField(this, 'از')"
                                    />
                                    <input 
                                        id="dateTo" 
                                        data-jdp 
                                        placeholder="تا تاریخ" 
                                        name="dateTo" 
                                        class="input-field"
                                        onchange="labelInField(this, 'تا')"
                                    />
                                </div>
                                <button class="btn btn-primary">
                                    اعمال فیلتر
                                </button>
                            </form>
                        </div>

                        <div class="flex items-center gap-3">
                            <button id="up" class="sort-btn">
                                <i class="fa-regular fa-arrow-up text-gray-700 text-lg"></i>
                            </button>
                            <input type="search" 
                                   id="search" 
                                   name="search-box" 
                                   placeholder="جستجو خطا"
                                   class="input-field w-64"/>
                        </div>

                        {% if pages %}
                            <div class="pagination">
                                <a href="{% if pages.has_previous %}?p={{ pages.previous_page_number }}{% else %}#{% endif %}"
                                   class="pagination-btn btn-primary {% if not pages.has_previous %}opacity-50 cursor-not-allowed{% endif %}">
                                    قبلی
                                </a>
                                <span class="text-sm whitespace-nowrap">
                                    صفحه {{ pages.number }}/{{ pages.paginator.num_pages }}
                                </span>
                                <a href="{% if pages.has_next %}?p={{ pages.next_page_number }}{% else %}#{% endif %}"
                                   class="pagination-btn btn-primary {% if not pages.has_next %}opacity-50 cursor-not-allowed{% endif %}">
                                    بعدی
                                </a>
                            </div>
                        {% endif %}

                        <!-- <button id='printbtn' class="btn btn-secondary">
                            چاپ
                        </button> -->
                    </div>
                </div>

                <div class="table-container">
                    <table id='table' class="table-modern">
                        <thead>
                            <tr>
                                <th>راه حل پیشنهادی</th>
                                <th>علت احتمالی</th>
                                <th>عنوان خطا</th>
                                <th>شماره خطا</th>
                                <th>ساعت</th>
                                <th>تاریخ</th>
                                <th>ردیف</th>
                            </tr>
                        </thead>
                        <tbody id="errorsTable">
                            {% if pages %}
                                {% for fault in pages.object_list %}
                                    <tr>
                                        <td title="{{ fault.errorcode|getDataOfFields:"solution" }}">
                                            {{ fault.errorcode|getDataOfFields:"solution"|truncatewords:5 }}
                                        </td>
                                        <td title="{{ fault.errorcode|getDataOfFields:"probablecause" }}">
                                            {{ fault.errorcode|getDataOfFields:"probablecause"|truncatewords:5 }}
                                        </td>
                                        <td title="{{ fault.errorcode|getDataOfFields:"errormessage" }}">
                                            {{ fault.errorcode|getDataOfFields:"errormessage"|truncatewords:3 }}
                                        </td>
                                        <td>{{ fault.errorcode }}</td>
                                        <td>{{ fault.created_at|date:'H:i' }}</td>
                                        <td>{{ fault.created_at|date:'Y-m-d'|convertDatesToHijri }}</td>
                                        <td>{{ forloop.counter0|add:pages.start_index }}</td>
                                    </tr>
                                {% endfor %}
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
  

    <script src="{% static 'js/jquery.js' %}"></script>
    <script>
        jalaliDatepicker.startWatch({
            minDate: "attr",
            maxDate: "today",
            hideAfterChange: true,
            autoHide: true,
            showTodayBtn: true,
            showEmptyBtn: true,
            persianDigits: true,
            topSpace: 10,
            bottomSpace: 30,
            dayRendering(opt, input) {
                return {
                    isHollyDay: opt.day == 1
                }
            }
        });
    </script>
    <script>
        let reversed = false;

        $(document).ready(function () {
            const errTable = $("#errorsTable");
            const tr = $("#errorsTable tr");
            $("#up").on("click", function () {
                const icon = $("#up i");
                icon.toggleClass("fa-arrow-up fa-arrow-down");
                const rows = [...tr]
                if (reversed) {
                    errTable.append(rows)
                } else {
                    errTable.append(rows.reverse())
                }
                reversed = !reversed
            });
            $("#search").on("keyup input", function () {
                let txt = $(this).val().toLowerCase()
                tr.filter(function () {
                    $(this).toggle($(this).text().toLowerCase().indexOf(txt) > -1)
                })
            })
            
            // Add toggle functionality for mobile view to show/hide columns
            if (window.innerWidth <= 768) {
                const tableHeaders = $(".table-modern th");
                tableHeaders.each(function(index) {
                    $(this).click(function() {
                        const columnIndex = index + 1;
                        $(".table-modern td:nth-child(" + columnIndex + ")").toggle();
                        $(this).toggleClass("active-column");
                    });
                });
            }
        });
    </script>
    <script>
        function labelInField(input, label) {
            const selectedDate = input.value;
            if (selectedDate) {
                input.value = `${label} ${selectedDate}`;
            }
        }
    </script>
    <script>
        $(document).ready(function() {
            $("#printbtn").on("click", function() {
                printTable();
            });

            function printTable() {
                const table = $("#table").clone();
                let columnsToRemove = [];

                table.find("tr").first().find("th, td").each(function(index, element) {
                    const columnText = $(element).text().trim();
                    if (columnText === "علت احتمالی" || columnText === "راه حل پیشنهادی") {
                        columnsToRemove.push(index);
                    }
                });

                table.find("tr").each(function() {
                    columnsToRemove.forEach(function(index) {
                        $(this).find("th, td").eq(index).remove();
                    });
                });

                table.find("tr").each(function(rowIndex) {
                    $(this).find("th, td").each(function(index, element) {
                        if (![2, 3, 4, 5, 6].includes(index)) {
                            $(element).remove();
                        }
                    });
                });

                table.find("td").each(function(index1) {
                    const fullTitle = $(this).attr("title");
                    if (fullTitle) {
                        $(this).text(fullTitle);
                    }
                });

                const printWindow = window.open('', '', 'height=1123,width=794');
                printWindow.document.write('<html><head>');
                printWindow.document.write('<style>');
                printWindow.document.write(`
                    @page {
                        size: A4;
                        margin: 10mm;
                    }
                    body {
                        width: 210mm;
                        height: 297mm;
                        margin: 0;
                        padding: 0;
                        font-family: system-ui, -apple-system, sans-serif;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 20px;
                        font-size: 16px;
                        font-weight: bold;
                        color: #1a1a1a;
                    }
                    table {
                        width: 100%;
                        margin: 0 auto;
                        border-collapse: collapse;
                    }
                    th, td {
                        border: 1px solid #000;
                        padding: 12px;
                        text-align: center;
                    }
                    th {
                        background-color: #f8fafc;
                        font-weight: 600;
                    }
                    @media print {
                        body {
                            width: 100%;
                            height: 100%;
                        }
                    }
                `);
                printWindow.document.write('</style></head><body>');

                const today = new Date().toLocaleDateString('fa-IR');
                printWindow.document.write(`
                    <div class="header">
                        گزارش خطاها - تاریخ: ${today}
                    </div>
                `);

                printWindow.document.write(table.wrap('<div>').parent().html());
                printWindow.document.write('</body></html>');
                printWindow.document.close();

                printWindow.document.title = "گزارش خطاها";

                printWindow.focus();
                printWindow.print();
                printWindow.close();
            }
        });
    </script>
{% endblock %}