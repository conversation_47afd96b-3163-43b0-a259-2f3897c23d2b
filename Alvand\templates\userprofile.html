{% extends "base.html" %} {% load static %} {% load userProfileTags %} {% block
head_extra %}
<link
  href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700;800&display=swap"
  rel="stylesheet"
/>
<link
  href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
  rel="stylesheet"
/>
<link rel="stylesheet" href="{% static 'css/dashboard-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-width-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/direct-layout-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-content.css' %}" />
<style>
  :root {
    --sidebar-width: 135px;
    --main-padding: 2rem;
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --primary-light: #eef2ff;
    --primary-dark: #3730a3;
    --secondary-color: #06b6d4;
    --secondary-hover: #0891b2;
    --secondary-light: #ecfeff;
    --secondary-dark: #0e7490;
    --accent-color: #f59e0b;
    --accent-hover: #d97706;
    --accent-light: #fef3c7;
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --danger-light: #fef2f2;
    --success-color: #22c55e;
    --success-hover: #16a34a;
    --success-light: #f0fdf4;
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --warning-light: #fef3c7;
    --text-color: #1e293b;
    --text-muted: #64748b;
    --text-light: #94a3b8;
    --text-lighter: #cbd5e1;
    --bg-color: #f8fafc;
    --bg-secondary: #f1f5f9;
    --bg-card: rgba(255, 255, 255, 0.95);
    --bg-glass: rgba(255, 255, 255, 0.1);
    --border-color: rgba(226, 232, 240, 0.6);
    --border-light: rgba(226, 232, 240, 0.3);
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.15);
    --radius-xs: 0.25rem;
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-2xl: 2rem;
    --radius-full: 9999px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-out;
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  html.dark {
    --primary-color: #818cf8;
    --primary-hover: #6366f1;
    --primary-light: #1e1b4b;
    --primary-dark: #312e81;
    --secondary-color: #22d3ee;
    --secondary-hover: #06b6d4;
    --secondary-light: #164e63;
    --secondary-dark: #0c4a6e;
    --accent-color: #fbbf24;
    --accent-hover: #f59e0b;
    --accent-light: #78350f;
    --danger-color: #f87171;
    --danger-hover: #ef4444;
    --danger-light: #7f1d1d;
    --success-color: #4ade80;
    --success-hover: #22c55e;
    --success-light: #14532d;
    --warning-color: #fbbf24;
    --warning-hover: #f59e0b;
    --warning-light: #78350f;
    --text-color: #f8fafc;
    --text-muted: #cbd5e1;
    --text-light: #94a3b8;
    --text-lighter: #64748b;
    --bg-color: #0f172a;
    --bg-secondary: #1e293b;
    --bg-card: rgba(30, 41, 59, 0.95);
    --bg-glass: rgba(0, 0, 0, 0.1);
    --border-color: rgba(51, 65, 85, 0.6);
    --border-light: rgba(51, 65, 85, 0.3);
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4),
      0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4),
      0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5),
      0 10px 10px -5px rgba(0, 0, 0, 0.4);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  * {
    box-sizing: border-box;
  }

  body {
    font-family: "Vazirmatn", "Inter", -apple-system, BlinkMacSystemFont,
      "Segoe UI", Roboto, sans-serif;
    background: var(--bg-color);
    background-image: radial-gradient(
        circle at 20% 80%,
        rgba(99, 102, 241, 0.08) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(6, 182, 212, 0.08) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 40% 40%,
        rgba(245, 158, 11, 0.05) 0%,
        transparent 50%
      ),
      linear-gradient(
        135deg,
        rgba(99, 102, 241, 0.02) 0%,
        rgba(6, 182, 212, 0.02) 100%
      );
    background-attachment: fixed;
    color: var(--text-color);
    line-height: 1.6;
    letter-spacing: -0.01em;
    font-weight: 400;
    min-height: 100vh;
    overflow-x: hidden;
  }

  html.dark body {
    background-image: radial-gradient(
        circle at 20% 80%,
        rgba(99, 102, 241, 0.15) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(6, 182, 212, 0.15) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 40% 40%,
        rgba(245, 158, 11, 0.1) 0%,
        transparent 50%
      ),
      linear-gradient(
        135deg,
        rgba(99, 102, 241, 0.05) 0%,
        rgba(6, 182, 212, 0.05) 100%
      );
  }

  .main-content {
    margin-right: var(--sidebar-width);
    margin-left: 0;
    width: calc(100% - var(--sidebar-width));
    padding: var(--main-padding);
  }

  /* RTL Support */
  html[dir="ltr"] .main-content {
    margin-right: 0;
    margin-left: var(--sidebar-width);
  }

  @media (max-width: 768px) {
    .main-content {
      margin-right: 0;
      margin-left: 0;
      width: 100%;
    }
  }

  /* Modern page header */
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2.5rem;
    padding: 2.5rem 3rem;
    background: linear-gradient(
      135deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%
    );
    border-radius: var(--radius-2xl);
    color: white;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
    transition: var(--transition-slow);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .page-header::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 20% 20%,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 40%
      ),
      radial-gradient(
        circle at 80% 80%,
        rgba(255, 255, 255, 0.08) 0%,
        transparent 40%
      ),
      linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.03) 50%,
        transparent 70%
      );
    pointer-events: none;
    animation: headerShimmer 8s ease-in-out infinite;
  }

  @keyframes headerShimmer {
    0%,
    100% {
      opacity: 0.8;
    }
    50% {
      opacity: 1;
    }
  }

  .page-header:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-2xl);
  }

  .page-title {
    font-size: 2.25rem;
    font-weight: 800;
    margin: 0;
    position: relative;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    letter-spacing: -0.02em;
  }

  .page-title::after {
    content: "";
    position: absolute;
    bottom: -12px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.3) 100%
    );
    border-radius: var(--radius-full);
    animation: titleUnderline 2s ease-in-out infinite alternate;
  }

  @keyframes titleUnderline {
    0% {
      width: 60px;
      opacity: 0.7;
    }
    100% {
      width: 100px;
      opacity: 1;
    }
  }

  .card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: var(--transition-slow);
    position: relative;
    border: 1px solid var(--border-light);
    -webkit-backdrop-filter: blur(16px);
    backdrop-filter: blur(16px);
  }

  .card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    );
    pointer-events: none;
    opacity: 0;
    transition: var(--transition);
  }

  .card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl);
    border-color: var(--border-color);
  }

  .card:hover::before {
    opacity: 1;
  }

  html.dark .card {
    border: 1px solid var(--border-light);
  }

  html.dark .card:hover {
    border-color: var(--border-color);
  }

  .card-header {
    padding: 2rem;
    background: linear-gradient(
      135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(248, 250, 252, 0.1) 100%
    );
    border-bottom: 1px solid var(--border-light);
    -webkit-backdrop-filter: blur(12px);
    backdrop-filter: blur(12px);
    position: relative;
  }

  html.dark .card-header {
    background: linear-gradient(
      135deg,
      rgba(30, 41, 59, 0.4) 0%,
      rgba(30, 41, 59, 0.1) 100%
    );
  }

  .card-body {
    padding: 2rem;
  }

  .nav-tabs {
    display: flex;
    background: linear-gradient(
      135deg,
      rgba(248, 250, 252, 0.8) 0%,
      rgba(248, 250, 252, 0.4) 100%
    );
    border-radius: var(--radius-xl);
    padding: 0.75rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
    -webkit-backdrop-filter: blur(12px);
    backdrop-filter: blur(12px);
    gap: 0.75rem;
    position: relative;
    z-index: 1;
    border: 1px solid var(--border-light);
    overflow: hidden;
  }

  .nav-tabs::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%
    );
    animation: tabsShimmer 3s ease-in-out infinite;
    pointer-events: none;
  }

  @keyframes tabsShimmer {
    0%,
    100% {
      transform: translateX(-100%);
    }
    50% {
      transform: translateX(100%);
    }
  }

  html.dark .nav-tabs {
    background: linear-gradient(
      135deg,
      rgba(30, 41, 59, 0.8) 0%,
      rgba(30, 41, 59, 0.4) 100%
    );
    border: 1px solid var(--border-light);
  }

  .nav-tab {
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: var(--text-muted);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-slow);
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    overflow: hidden;
    font-size: 0.875rem;
    white-space: nowrap;
  }

  .nav-tab::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(99, 102, 241, 0.1) 0%,
      rgba(6, 182, 212, 0.1) 100%
    );
    opacity: 0;
    transition: var(--transition);
    border-radius: var(--radius-lg);
  }

  .nav-tab i {
    font-size: 1.25rem;
    transition: var(--transition);
    position: relative;
    z-index: 1;
  }

  .nav-tab:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
  }

  .nav-tab:hover::before {
    opacity: 1;
  }

  .nav-tab:hover i {
    transform: scale(1.1) rotate(5deg);
  }

  .nav-tab.active {
    color: white;
    background: linear-gradient(
      135deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%
    );
    box-shadow: var(--shadow-glow);
    transform: translateY(-3px);
  }

  .nav-tab.active::before {
    opacity: 0;
  }

  .nav-tab.active::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.2) 0%,
      transparent 70%
    );
    border-radius: var(--radius-lg);
    animation: activeTabPulse 2s ease-in-out infinite;
  }

  .nav-tab.active i {
    transform: scale(1.15);
  }

  @keyframes activeTabPulse {
    0%,
    100% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
  }

  /* Tab content transitions */
  .tab-content {
    transition: var(--transition);
    animation: fadeIn 0.3s ease forwards;
  }

  .tab-content.hidden {
    display: none;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Modal styling */
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .modal-backdrop.show {
    opacity: 1;
    visibility: visible;
  }

  .modal-content {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 500px;
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(12px);
  }

  .modal-backdrop.show .modal-content {
    transform: scale(1);
    opacity: 1;
  }

  .modal-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(
      to right,
      rgba(99, 102, 241, 0.05),
      rgba(99, 102, 241, 0.02)
    );
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  }

  .modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    background: linear-gradient(to right, var(--text-color), var(--text-muted));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .modal-close {
    background: transparent;
    border: none;
    font-size: 1.5rem;
    line-height: 1;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .modal-close:hover {
    color: var(--danger-color);
    background-color: rgba(244, 63, 94, 0.1);
    transform: rotate(90deg);
  }

  .modal-body {
    padding: 1.5rem;
  }

  .modal-footer {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.75rem;
    background: linear-gradient(
      to right,
      rgba(99, 102, 241, 0.02),
      rgba(99, 102, 241, 0.05)
    );
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  }

  /* Modern feature cards */
  .feature-card {
    border-radius: var(--radius-xl);
    padding: 2rem;
    background: var(--bg-card);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-md);
    transition: var(--transition-slow);
    position: relative;
    overflow: hidden;
    height: 100%;
    -webkit-backdrop-filter: blur(12px);
    backdrop-filter: blur(12px);
  }

  .feature-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(
      180deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%
    );
    transition: var(--transition-slow);
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
    opacity: 0.8;
  }

  .feature-card::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.05) 0%,
      transparent 50%
    );
    opacity: 0;
    transition: var(--transition);
    pointer-events: none;
  }

  .feature-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-color);
  }

  .feature-card:hover::before {
    width: 8px;
    opacity: 1;
  }

  .feature-card:hover::after {
    opacity: 1;
  }

  .feature-card.warning::before {
    background: linear-gradient(
      to bottom,
      var(--warning-color),
      var(--warning-hover)
    );
  }

  .feature-card.danger::before {
    background: linear-gradient(
      to bottom,
      var(--danger-color),
      var(--danger-hover)
    );
  }

  .feature-card.success::before {
    background: linear-gradient(
      to bottom,
      var(--success-color),
      var(--success-hover)
    );
  }

  .feature-card.info::before {
    background: linear-gradient(
      to bottom,
      var(--secondary-color),
      var(--secondary-hover)
    );
  }

  .feature-card.primary::before {
    background: linear-gradient(
      to bottom,
      var(--primary-color),
      var(--primary-hover)
    );
  }

  html.dark .feature-card {
    border: 1px solid rgba(51, 65, 85, 0.4);
  }

  html.dark .feature-card:hover {
    border-color: rgba(51, 65, 85, 0.6);
  }

  .feature-card .icon {
    width: 72px;
    height: 72px;
    border-radius: var(--radius-xl);
    background: linear-gradient(
      135deg,
      var(--primary-light) 0%,
      rgba(99, 102, 241, 0.15) 100%
    );
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-lg);
    position: relative;
    z-index: 1;
    overflow: hidden;
    transition: var(--transition-slow);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .feature-card .icon::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 30% 30%,
      rgba(255, 255, 255, 0.3) 0%,
      transparent 60%
    );
    border-radius: var(--radius-xl);
    transition: var(--transition);
  }

  .feature-card .icon::after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1),
      transparent 50%,
      rgba(255, 255, 255, 0.1)
    );
    z-index: -1;
    border-radius: var(--radius-xl);
    transition: var(--transition);
    opacity: 0;
  }

  .feature-card:hover .icon {
    transform: scale(1.1) rotate(-5deg);
    box-shadow: var(--shadow-2xl);
  }

  .feature-card:hover .icon::after {
    opacity: 1;
    transform: rotate(180deg);
  }

  .feature-card.warning .icon {
    background: linear-gradient(
      135deg,
      var(--warning-light),
      rgba(245, 158, 11, 0.2)
    );
    color: var(--warning-color);
    box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.2);
  }

  .feature-card.danger .icon {
    background: linear-gradient(
      135deg,
      var(--danger-light),
      rgba(244, 63, 94, 0.2)
    );
    color: var(--danger-color);
    box-shadow: 0 10px 15px -3px rgba(244, 63, 94, 0.2);
  }

  .feature-card.success .icon {
    background: linear-gradient(
      135deg,
      var(--success-light),
      rgba(16, 185, 129, 0.2)
    );
    color: var(--success-color);
    box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.2);
  }

  .feature-card.info .icon {
    background: linear-gradient(
      135deg,
      var(--secondary-light),
      rgba(79, 209, 197, 0.2)
    );
    color: var(--secondary-color);
    box-shadow: 0 10px 15px -3px rgba(79, 209, 197, 0.2);
  }

  .feature-card.primary .icon {
    background: linear-gradient(
      135deg,
      var(--primary-light),
      rgba(138, 125, 234, 0.2)
    );
    color: var(--primary-color);
    box-shadow: 0 10px 15px -3px rgba(138, 125, 234, 0.2);
  }

  .feature-card .title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-color);
    position: relative;
    display: inline-block;
  }

  .feature-card .title::after {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(
      to right,
      var(--primary-color),
      var(--secondary-color)
    );
    transition: width 0.3s ease;
    border-radius: 1px;
  }

  .feature-card:hover .title::after {
    width: 100%;
  }

  .feature-card .description {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 1.25rem;
  }

  /* Action buttons */
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.875rem 1.25rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
    box-shadow: var(--shadow-sm);
    background: transparent;
    backdrop-filter: blur(8px);
  }

  .action-button::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0)
    );
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: 1;
  }

  .action-button:hover::before {
    transform: translateX(100%);
  }

  .action-button .icon {
    margin-left: 0.75rem;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
  }

  .action-button:hover .icon {
    transform: translateX(4px) scale(1.1);
  }

  .action-button span {
    position: relative;
    z-index: 2;
  }

  .action-button.primary {
    background: linear-gradient(
      to right,
      rgba(138, 125, 234, 0.1),
      rgba(138, 125, 234, 0.05)
    );
    color: var(--primary-color);
    border-color: var(--primary-color);
  }

  .action-button.primary:hover {
    background: linear-gradient(
      to right,
      var(--primary-color),
      var(--primary-hover)
    );
    color: white;
    box-shadow: 0 5px 15px rgba(138, 125, 234, 0.25);
    transform: translateY(-2px);
  }

  .action-button.success {
    background: linear-gradient(
      to right,
      rgba(79, 209, 197, 0.1),
      rgba(79, 209, 197, 0.05)
    );
    color: var(--secondary-color);
    border-color: var(--secondary-color);
  }

  .action-button.success:hover {
    background: linear-gradient(
      to right,
      var(--secondary-color),
      var(--secondary-hover)
    );
    color: white;
    box-shadow: 0 5px 15px rgba(79, 209, 197, 0.25);
    transform: translateY(-2px);
  }

  .action-button.danger {
    background: linear-gradient(
      to right,
      rgba(244, 63, 94, 0.1),
      rgba(244, 63, 94, 0.05)
    );
    color: var(--danger-color);
    border-color: var(--danger-color);
  }

  .action-button.danger:hover {
    background: linear-gradient(
      to right,
      var(--danger-color),
      var(--danger-hover)
    );
    color: white;
    box-shadow: 0 5px 15px rgba(244, 63, 94, 0.25);
    transform: translateY(-2px);
  }

  .action-button.warning {
    background: linear-gradient(
      to right,
      rgba(245, 158, 11, 0.1),
      rgba(245, 158, 11, 0.05)
    );
    color: var(--warning-color);
    border-color: var(--warning-color);
  }

  .action-button.warning:hover {
    background: linear-gradient(
      to right,
      var(--warning-color),
      var(--warning-hover)
    );
    color: white;
    box-shadow: 0 5px 15px rgba(245, 158, 11, 0.25);
    transform: translateY(-2px);
  }

  /* Add pulse animation for interactive elements */
  @keyframes pulse-border {
    0% {
      box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
  }

  .btn-primary:focus,
  .action-button.success:focus {
    animation: pulse-border 1.5s infinite;
  }

  /* Add smooth hover transitions for cards */
  .card {
    transition: transform 0.3s ease, box-shadow 0.3s ease,
      border-color 0.3s ease;
  }

  /* Add subtle animations for better user experience */
  .feature-card .icon {
    transition: transform 0.3s ease, border-radius 0.3s ease,
      box-shadow 0.3s ease;
  }

  /* Add floating animation to the avatar */
  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  .avatar-container:hover .avatar {
    animation: float 3s ease-in-out infinite;
  }

  /* Add more micro-interactions */
  .form-control:focus {
    transform: scale(1.01);
  }

  .dropdown-toggle:hover {
    transform: translateY(-2px);
  }

  .modal-backdrop.show .modal-content {
    animation: modalPop 0.5s forwards;
  }

  @keyframes modalPop {
    0% {
      transform: scale(0.9);
      opacity: 0;
    }
    40% {
      transform: scale(1.05);
    }
    60% {
      transform: scale(0.98);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .form-group {
    margin-bottom: 1.5rem;
    position: relative;
  }

  .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.875rem;
    transition: var(--transition);
  }

  .form-control {
    display: block;
    width: 100%;
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-color);
    background-color: transparent;
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
  }

  .form-control:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.25);
  }

  .form-control:hover:not(:focus) {
    border-color: var(--primary-hover);
  }

  .form-group.focused .form-label {
    color: var(--primary-color);
    transform: translateY(-2px);
  }

  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    transition: var(--transition);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
    z-index: 1;
    font-size: 0.875rem;
  }

  .btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 0.2),
      rgba(255, 255, 255, 0)
    );
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: -1;
  }

  .btn:hover::before {
    transform: translateX(100%);
  }

  .btn:active {
    transform: translateY(1px);
  }

  .btn-icon {
    margin-left: 0.5rem;
    transition: var(--transition);
  }

  .btn:hover .btn-icon {
    transform: translateX(3px);
  }

  .btn-primary {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }

  .btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    box-shadow: 0 5px 15px rgba(138, 125, 234, 0.35);
  }

  .btn-secondary {
    color: white;
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
  }

  .btn-secondary:hover {
    background-color: var(--secondary-hover);
    border-color: var(--secondary-hover);
    box-shadow: 0 5px 15px rgba(79, 209, 197, 0.35);
  }

  .btn-danger {
    color: white;
    background-color: var(--danger-color);
    border-color: var(--danger-color);
  }

  .btn-danger:hover {
    background-color: var(--danger-hover);
    border-color: var(--danger-hover);
    box-shadow: 0 5px 15px rgba(244, 63, 94, 0.35);
  }

  .btn-success {
    color: white;
    background-color: var(--success-color);
    border-color: var(--success-color);
  }

  .btn-success:hover {
    background-color: var(--success-hover);
    border-color: var(--success-hover);
    box-shadow: 0 5px 15px rgba(16, 185, 129, 0.35);
  }

  .btn-warning {
    color: white;
    background-color: var(--warning-color);
    border-color: var(--warning-color);
  }

  .btn-warning:hover {
    background-color: var(--warning-hover);
    border-color: var(--warning-hover);
    box-shadow: 0 5px 15px rgba(245, 158, 11, 0.35);
  }

  .avatar-container {
    position: relative;
    margin-bottom: 2.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .avatar {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    object-fit: cover;
    border: 6px solid white;
    background-color: var(--bg-color);
    padding: 6px;
    box-shadow: var(--shadow-xl);
    transition: var(--transition-slow);
    position: relative;
    z-index: 2;
  }

  .avatar:hover {
    transform: scale(1.08);
    box-shadow: var(--shadow-2xl);
  }

  .avatar::after {
    content: "";
    position: absolute;
    top: -12px;
    left: -12px;
    right: -12px;
    bottom: -12px;
    border-radius: 50%;
    background: conic-gradient(
      from 0deg,
      var(--primary-color) 0%,
      var(--secondary-color) 50%,
      var(--accent-color) 75%,
      var(--primary-color) 100%
    );
    z-index: -1;
    animation: avatarRotate 6s linear infinite;
    opacity: 0.9;
    filter: blur(8px);
  }

  @keyframes avatarRotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .avatar-upload {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: linear-gradient(
      135deg,
      var(--secondary-color) 0%,
      var(--primary-color) 100%
    );
    color: white;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-slow);
    border: 3px solid white;
    z-index: 3;
    overflow: hidden;
    font-size: 1.125rem;
  }

  .avatar-upload::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      circle at 30% 30%,
      rgba(255, 255, 255, 0.4) 0%,
      transparent 70%
    );
    border-radius: 50%;
    transition: var(--transition);
  }

  .avatar-upload:hover {
    transform: scale(1.15) rotate(15deg);
    box-shadow: var(--shadow-xl);
  }

  .avatar-upload:hover::before {
    background: radial-gradient(
      circle at 30% 30%,
      rgba(255, 255, 255, 0.6) 0%,
      transparent 70%
    );
  }

  .form-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
  }

  .form-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .form-switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--text-light);
    transition: 0.4s;
    border-radius: 34px;
  }

  .form-switch .slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
  }

  .form-switch input:checked + .slider {
    background: linear-gradient(
      to right,
      var(--primary-color),
      var(--secondary-color)
    );
  }

  .form-switch input:focus + .slider {
    box-shadow: 0 0 1px var(--primary-color);
  }

  .form-switch input:checked + .slider:before {
    transform: translateX(26px);
  }

  .dropdown {
    position: relative;
  }

  .dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.625rem 1rem;
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .dropdown-toggle:hover {
    border-color: var(--primary-color);
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    display: none;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 0.875rem;
    color: var(--text-color);
    text-align: left;
    list-style: none;
    background-color: var(--bg-card);
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(8px);
  }

  .dropdown-menu.show {
    display: block;
    animation: fadeIn 0.3s ease;
  }

  .dropdown-item {
    display: block;
    width: 100%;
    padding: 0.5rem 1.5rem;
    clear: both;
    font-weight: 400;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    cursor: pointer;
  }

  .dropdown-item:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
  }

  /* Bottom action buttons */
  .action-button-save {
    background: linear-gradient(
      to right,
      rgba(79, 209, 197, 0.1),
      rgba(79, 209, 197, 0.05)
    );
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    padding: 1rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    width: 100%;
  }

  .action-button-save:hover {
    background: linear-gradient(
      to right,
      var(--secondary-color),
      var(--secondary-hover)
    );
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(79, 209, 197, 0.25);
  }

  .action-button-delete {
    background: linear-gradient(
      to right,
      rgba(244, 63, 94, 0.1),
      rgba(244, 63, 94, 0.05)
    );
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
    padding: 1rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    width: 100%;
  }

  .action-button-delete:hover {
    background: linear-gradient(
      to right,
      var(--danger-color),
      var(--danger-hover)
    );
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(244, 63, 94, 0.25);
  }

  /* Save & Delete user buttons at the page bottom */
  #ذخیره_کاربر {
    background: linear-gradient(
      to right,
      rgba(79, 209, 197, 0.1),
      rgba(79, 209, 197, 0.05)
    );
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
  }

  #ذخیره_کاربر:hover {
    background: linear-gradient(
      to right,
      var(--secondary-color),
      var(--secondary-hover)
    );
    color: white;
  }

  #حذف_کاربر {
    background: linear-gradient(
      to right,
      rgba(244, 63, 94, 0.1),
      rgba(244, 63, 94, 0.05)
    );
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
  }

  #حذف_کاربر:hover {
    background: linear-gradient(
      to right,
      var(--danger-color),
      var(--danger-hover)
    );
    color: white;
  }

  /* User card header */
  .user-card-header {
    height: 7rem;
    background: linear-gradient(
      135deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%
    );
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    position: relative;
    overflow: hidden;
  }

  .user-card-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.15;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3Ccircle cx='10' cy='10' r='2'/%3E%3Ccircle cx='50' cy='10' r='2'/%3E%3Ccircle cx='10' cy='50' r='2'/%3E%3Ccircle cx='50' cy='50' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 30px 30px;
    animation: patternMove 20s linear infinite;
  }

  @keyframes patternMove {
    0% {
      transform: translate(0, 0);
    }
    100% {
      transform: translate(30px, 30px);
    }
  }
</style>
{% endblock %} {% block main %}
<div class="main-content">
  <!-- Page Header -->
  <div class="page-header">
    <div>
      <h1 class="page-title">{{ pageTitle }}</h1>
      <p class="text-white text-opacity-80 mt-1">
        مدیریت کاربران و تنظیمات دسترسی
      </p>
    </div>

    <button id="btnpass" class="btn btn-primary flex items-center">
      <i class="fa-solid fa-key mr-2"></i>
      بازنگاری رمز عبور
    </button>
  </div>

  <!-- Main Content -->
  <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
    <!-- Sidebar Column -->
    <div class="md:col-span-3">
      <!-- User Card -->
      <div class="card overflow-visible mb-6">
        <div class="card-body p-0">
          <div class="user-card-header">
            <div class="user-card-pattern"></div>
          </div>

          <div class="avatar-container -mt-16 px-4">
            <img src="" id="id_picurl" class="avatar" alt="تصویر کاربر" />
            <label for="upload" class="avatar-upload">
              <i class="fa-solid fa-camera"></i>
            </label>
            <input type="file" id="upload" class="hidden" name="uploadPhoto" />
          </div>

          <div class="p-4">
            <div class="form-group">
              <label class="form-label">ویرایش/اضافه:</label>
              {{ userform.editOrAdd }}
            </div>

            <div class="form-group">
              <label class="form-label">نام کاربری:</label>
              {{ userform.username }}
            </div>

            <div class="form-group flex items-center justify-between">
              <label class="form-label mb-0">وضعیت حساب:</label>
              <label class="form-switch">
                {{ userform.active }}
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <input type="submit" id="save" class="hidden" name="saveUser" />
        <label
          for="save"
          class="action-button success flex justify-center items-center"
        >
          <span>ذخیره کاربر</span>
          <i class="fa-solid fa-save icon"></i>
        </label>

        <button
          type="button"
          id="delUserModalBtn"
          class="action-button danger flex justify-center items-center"
        >
          <span>حذف کاربر</span>
          <i class="fa-solid fa-trash icon"></i>
        </button>

        <button
          type="button"
          id="delUserProfile"
          class="action-button danger flex justify-center items-center"
        >
          <span>حذف پروفایل</span>
          <i class="fa-solid fa-user-slash icon"></i>
        </button>
      </div>
    </div>

    <!-- Main Form Column -->
    <div class="md:col-span-9">
      <div class="card">
        <form method="post" id="form" enctype="multipart/form-data">
          {% csrf_token %}

          <!-- Tabs -->
          <div class="card-header px-6 py-0">
            <div class="nav-tabs">
              <div class="nav-tab active" data-tab="account-info">
                <i class="fa-solid fa-user mr-2"></i>حساب کاربری
              </div>
              <div class="nav-tab" data-tab="personal-info">
                <i class="fa-solid fa-id-card mr-2"></i>اطلاعات شخصی
              </div>
              <div class="nav-tab" data-tab="financial-info">
                <i class="fa-solid fa-credit-card mr-2"></i>اطلاعات مالی
              </div>
              <div class="nav-tab" data-tab="permissions">
                <i class="fa-solid fa-lock mr-2"></i>دسترسی‌ها
              </div>
            </div>
          </div>

          <div class="card-body">
            <!-- Account Info Tab -->
            <div class="tab-content active" id="account-info-tab">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="feature-card">
                  <div class="icon">
                    <i class="fa-solid fa-user"></i>
                  </div>
                  <h3 class="title">اطلاعات کاربری</h3>

                  <div class="form-group">
                    <label class="form-label">نام:</label>
                    {{ userform.name }}
                  </div>

                  <div class="form-group">
                    <label class="form-label">نام خانوادگی:</label>
                    {{ userform.lastname }}
                  </div>

                  <div class="form-group">
                    <label class="form-label">ایمیل:</label>
                    {{ userform.email }}
                  </div>

                  <div class="form-group">
                    <label class="form-label">داخلی:</label>
                    {{ userform.extension }}
                  </div>

                  <div class="form-group">
                    <label class="form-label" id="id_label_groupname"
                      >نقش:</label
                    >
                    {{ userform.groupname }}
                  </div>
                </div>

                <div class="feature-card info">
                  <div class="icon">
                    <i class="fa-solid fa-phone"></i>
                  </div>
                  <h3 class="title">اطلاعات تماس</h3>

                  <div class="form-group">
                    <label class="form-label">تلفن:</label>
                    {{ infosform.telephone }}
                  </div>

                  <div class="form-group">
                    <label class="form-label">شماره همراه:</label>
                    {{ infosform.phonenumber }}
                  </div>

                  <div class="form-group">
                    <label class="form-label">استان:</label>
                    {{ infosform.province }}
                  </div>

                  <div class="form-group">
                    <label class="form-label">شهر:</label>
                    {{ infosform.city }}
                  </div>
                </div>

                <div class="feature-card success">
                  <div class="icon">
                    <i class="fa-solid fa-gear"></i>
                  </div>
                  <h3 class="title">تنظیمات</h3>

                  <div class="form-group">
                    <label class="form-label">دسترسی به داخلی:</label>
                    <div class="dropdown">
                      <button
                        type="button"
                        id="accessToExtsBtn"
                        class="dropdown-toggle"
                      >
                        <span>انتخاب داخلی‌ها</span>
                        <i class="fa-solid fa-chevron-down"></i>
                      </button>
                      <div id="accessToExtsDp" class="dropdown-menu">
                        <ul class="py-2">
                          {% for ext in userform.usersextension %}
                          <li class="dropdown-item">{{ ext }}</li>
                          {% endfor %}
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div class="form-group mt-4">
                    <label class="form-label">آدرس:</label>
                    {{ infosform.address }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Personal Info Tab -->
            <div class="tab-content hidden" id="personal-info-tab">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="feature-card">
                  <div class="icon">
                    <i class="fa-solid fa-id-card"></i>
                  </div>
                  <h3 class="title">اطلاعات هویتی</h3>

                  <div class="form-group">
                    <label class="form-label">کد ملی:</label>
                    {{ infosform.nationalcode }}
                  </div>

                  <div class="form-group">
                    <label class="form-label">تاریخ تولد:</label>
                    {{ infosform.birthdate }}
                  </div>

                  <div class="form-group">
                    <label class="form-label">جنسیت:</label>
                    {{ infosform.gender }}
                  </div>
                </div>

                <div class="feature-card warning">
                  <div class="icon">
                    <i class="fa-solid fa-user-group"></i>
                  </div>
                  <h3 class="title">وضعیت</h3>

                  <div class="form-group">
                    <label class="form-label">وضعیت تاهل:</label>
                    {{ infosform.maritalstatus }}
                  </div>

                  <div id="military-container" class="form-group">
                    <label class="form-label" id="labelmilitary"
                      >وضعیت نظام وظیفه:</label
                    >
                    {{ infosform.military }}
                  </div>
                </div>

                <div class="feature-card info">
                  <div class="icon">
                    <i class="fa-solid fa-graduation-cap"></i>
                  </div>
                  <h3 class="title">تحصیلات</h3>

                  <div class="form-group">
                    <label class="form-label">مدرک:</label>
                    {{ infosform.educationdegree }}
                  </div>

                  <div class="form-group">
                    <label class="form-label">زمینه مدرک:</label>
                    {{ infosform.educationfield }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Financial Info Tab -->
            <div class="tab-content hidden" id="financial-info-tab">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="feature-card success">
                  <div class="icon">
                    <i class="fa-solid fa-money-bill"></i>
                  </div>
                  <h3 class="title">اطلاعات بانکی</h3>

                  <div class="form-group">
                    <label class="form-label">شماره شبا:</label>
                    {{ infosform.accountnumbershaba }}
                  </div>
                </div>

                <div class="feature-card primary">
                  <div class="icon">
                    <i class="fa-solid fa-credit-card"></i>
                  </div>
                  <h3 class="title">کارت بانکی</h3>

                  <div class="form-group">
                    <label class="form-label">شماره کارت:</label>
                    {{ infosform.cardnumber }}
                  </div>
                </div>

                <div class="feature-card warning">
                  <div class="icon">
                    <i class="fa-solid fa-piggy-bank"></i>
                  </div>
                  <h3 class="title">حساب بانکی</h3>

                  <div class="form-group">
                    <label class="form-label">شماره حساب:</label>
                    {{ infosform.accountnumber }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Permissions Tab -->
            <div class="tab-content hidden" id="permissions-tab">
              <div class="feature-card">
                <div class="icon">
                  <i class="fa-solid fa-shield-halved"></i>
                </div>
                <h3 class="title">سطح دسترسی</h3>
                <p class="description mb-4">
                  تعیین سطوح دسترسی کاربر به بخش‌های مختلف سیستم
                </p>

                <div class="form-group">
                  <div class="dropdown">
                    <button
                      type="button"
                      id="accessLevelBtn"
                      class="dropdown-toggle"
                    >
                      <span>انتخاب سطح دسترسی</span>
                      <i class="fa-solid fa-chevron-down"></i>
                    </button>
                    <div id="accessLevelDp" class="dropdown-menu">
                      <div class="p-3">
                        <div
                          class="py-2 border-b border-gray-200 dark:border-gray-700 mb-2"
                        >
                          <div class="flex items-center">
                            <input
                              type="checkbox"
                              id="allPerm"
                              class="w-4 h-4 text-primary-color border-gray-300 rounded mr-2"
                            />
                            <label for="allPerm">تمامی مجوز‌ها</label>
                          </div>
                        </div>
                        {% for perm in permform %}
                        <div class="py-1">
                          <div class="flex items-center">
                            {{ perm }}
                            <label for="{{ perm.id_for_label }}" class="mr-2"
                              >{{ perm.label }}</label
                            >
                          </div>
                        </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Password Reset Modal -->
<div id="changepass" class="modal-backdrop">
  <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">بازنگاری رمز عبور</h3>
      <button id="cancelChpass" class="modal-close">&times;</button>
    </div>
    <div class="modal-body text-center">
      <div class="flex flex-col items-center">
        <div
          class="w-16 h-16 flex items-center justify-center rounded-full bg-primary-light text-primary-color mb-4"
        >
          <i class="fa-solid fa-key text-2xl"></i>
        </div>
        <h4 class="text-xl font-semibold mb-2">بازنگاری رمز عبور</h4>
        <p class="text-gray-600 dark:text-gray-400 mb-1">
          آیا از انجام عملیات تغییر رمز عبور اطمینان دارید؟
        </p>
        <p class="text-primary-color text-sm">این عملیات قابل بازگشت نیست.</p>
      </div>
    </div>
    <div class="modal-footer">
      <button id="cancelChpass2" class="btn btn-secondary">لغو</button>
      <input
        type="submit"
        id="confirmChangePass"
        class="hidden"
        name="ChangePassword"
        value="submit"
      />
      <label for="confirmChangePass" class="btn btn-primary">تایید</label>
    </div>
  </div>
</div>

<!-- Delete User Modal -->
<div id="deluser" class="modal-backdrop">
  <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">حذف کاربر</h3>
      <button id="canceldelete" class="modal-close">&times;</button>
    </div>
    <div class="modal-body text-center">
      <div class="flex flex-col items-center">
        <div
          class="w-16 h-16 flex items-center justify-center rounded-full bg-danger-light text-danger-color mb-4"
        >
          <i class="fa-solid fa-trash text-2xl"></i>
        </div>
        <h4 class="text-xl font-semibold mb-2">حذف کاربر</h4>
        <p class="text-gray-600 dark:text-gray-400 mb-1">
          آیا از انجام عملیات حذف کاربر اطمینان دارید؟
        </p>
        <p class="text-danger-color text-sm">این عملیات قابل بازگشت نیست!</p>
      </div>
    </div>
    <div class="modal-footer">
      <button id="canceldelete2" class="btn btn-secondary">لغو</button>
      <input
        type="submit"
        id="confirm"
        class="hidden"
        name="deleteUser"
        value="submit"
      />
      <label for="confirm" class="btn btn-danger">تایید</label>
    </div>
  </div>
</div>

<!-- Delete Profile Modal -->
<div id="delpro" class="modal-backdrop">
  <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">حذف پروفایل</h3>
      <button id="cancelDelPro" class="modal-close">&times;</button>
    </div>
    <div class="modal-body text-center">
      <div class="flex flex-col items-center">
        <div
          class="w-16 h-16 flex items-center justify-center rounded-full bg-danger-light text-danger-color mb-4"
        >
          <i class="fa-solid fa-user-slash text-2xl"></i>
        </div>
        <h4 class="text-xl font-semibold mb-2">حذف پروفایل</h4>
        <p class="text-gray-600 dark:text-gray-400 mb-1">
          آیا از انجام عملیات حذف پروفایل کاربر اطمینان دارید؟
        </p>
        <p class="text-danger-color text-sm">این عملیات قابل بازگشت نیست!</p>
      </div>
    </div>
    <div class="modal-footer">
      <button id="cancelDelPro2" class="btn btn-secondary">لغو</button>
      <input
        type="submit"
        id="confirmDelPro"
        class="hidden"
        name="deleteProfile"
        value="submit"
      />
      <label for="confirmDelPro" class="btn btn-danger">تایید</label>
    </div>
  </div>
</div>

<div class="main-content">
  <!-- Form action buttons at the bottom of the page -->
  <div class="flex justify-between mt-6 gap-4">
    <button
      type="button"
      id="ذخیره کاربر"
      class="btn btn-secondary w-full flex items-center justify-center gap-2"
    >
      <i class="fa-solid fa-save"></i>
      ذخیره کاربر
    </button>
    <button
      type="button"
      id="حذف کاربر"
      class="btn btn-danger w-full flex items-center justify-center gap-2"
    >
      <i class="fa-solid fa-trash"></i>
      حذف کاربر
    </button>
  </div>
</div>
{% endblock %} {% block extrascripts %} {{ users|json_script:"users-data" }} {{
session_user|json_script:"session-user-data" }} {{
contactInfos|json_script:"contact-infos-data" }}

<script>
  $(document).ready(function () {
    // Initialize UI components
    enhanceFormControls();
    initTabs();
    initModals();
    initDropdowns();
    setupFormHandlers();
    initUserDataHandling();
    applyRippleEffect();
    setupCardAnimations();

    // Function to enhance form controls with custom styling
    function enhanceFormControls() {
      // Add classes to form elements for consistent styling
      $(
        'input[type="text"], input[type="email"], input[type="number"], input[type="password"], textarea'
      )
        .addClass("form-control")
        .on("focus", function () {
          $(this).parent().addClass("focused");
        })
        .on("blur", function () {
          $(this).parent().removeClass("focused");
        });

      // Style select elements
      $("select").addClass("form-control");

      // Handle military field visibility based on gender
      $("#id_gender")
        .on("change", function () {
          if ($(this).val() === "1") {
            // Female
            $("#military-container").slideUp(300);
          } else {
            $("#military-container").slideDown(300);
          }
        })
        .trigger("change"); // Trigger on page load
    }

    // Function to initialize tabs with smooth transitions
    function initTabs() {
      $(".nav-tab").on("click", function () {
        // Remove active class from all tabs
        $(".nav-tab").removeClass("active");

        // Add active class to clicked tab
        $(this).addClass("active");

        // Get the target tab content ID
        const targetId = $(this).data("tab");

        // Hide all tab content with animation
        $(".tab-content").fadeOut(200);

        // Show the target tab content with animation
        setTimeout(function () {
          $(".tab-content").removeClass("active").addClass("hidden");
          $(`#${targetId}-tab`)
            .removeClass("hidden")
            .addClass("active")
            .fadeIn(300);
        }, 200);
      });
    }

    // Function to initialize modals with fancy animations
    function initModals() {
      // Helper function to setup each modal
      function setupModal(modalId, triggerBtnId, closeBtnIds) {
        const modal = $(`#${modalId}`);
        const triggerBtn = $(`#${triggerBtnId}`);

        // Open modal with animation
        triggerBtn.on("click", function () {
          modal.addClass("show");
          setTimeout(() => {
            modal.find(".modal-content").css({
              transform: "scale(1)",
              opacity: "1",
            });
          }, 50);
        });

        // Close modal handlers
        closeBtnIds.forEach((btnId) => {
          $(`#${btnId}`).on("click", function () {
            modal.find(".modal-content").css({
              transform: "scale(0.9)",
              opacity: "0",
            });
            setTimeout(() => {
              modal.removeClass("show");
            }, 300);
          });
        });
      }

      // Setup each modal
      setupModal("changepass", "btnpass", ["cancelChpass", "cancelChpass2"]);
      setupModal("deluser", "delUserModalBtn", [
        "canceldelete",
        "canceldelete2",
      ]);
      setupModal("delpro", "delUserProfile", ["cancelDelPro", "cancelDelPro2"]);

      // Handle form submissions from modals
      $("#confirmChangePass")
        .parent()
        .on("click", function () {
          $("#form").append(
            '<input type="hidden" name="ChangePassword" value="submit">'
          );
          $("#form").submit();
        });

      $("#confirm")
        .parent()
        .on("click", function () {
          $("#form").append(
            '<input type="hidden" name="deleteUser" value="submit">'
          );
          $("#form").submit();
        });

      $("#confirmDelPro")
        .parent()
        .on("click", function () {
          $("#form").append(
            '<input type="hidden" name="deleteProfile" value="submit">'
          );
          $("#form").submit();
        });
    }

    // Function to initialize dropdowns with animation
    function initDropdowns() {
      function setupDropdown(toggleId, menuId) {
        const toggle = $(`#${toggleId}`);
        const menu = $(`#${menuId}`);

        toggle.on("click", function (e) {
          e.stopPropagation();

          // Get all dropdowns except the current one
          $(".dropdown-menu").not(menu).removeClass("show");

          // Toggle the current dropdown
          menu.toggleClass("show");
        });

        // Close when clicking outside
        $(document).on("click", function (e) {
          if (
            !$(e.target).closest(`#${toggleId}, #${menuId}`).length &&
            menu.hasClass("show")
          ) {
            menu.removeClass("show");
          }
        });
      }

      // Setup dropdowns
      setupDropdown("accessToExtsBtn", "accessToExtsDp");
      setupDropdown("accessLevelBtn", "accessLevelDp");
    }

    // Setup form field handlers and validation
    function setupFormHandlers() {
      // Handle "select all" permissions checkbox
      const allPerm = $("#allPerm");
      const permCheckboxes = $(".perm-checkbox");

      allPerm.on("change", function () {
        const isChecked = $(this).prop("checked");
        permCheckboxes.prop("checked", isChecked);
      });

      permCheckboxes.on("change", function () {
        let allChecked = true;
        permCheckboxes.each(function () {
          if (!$(this).prop("checked")) {
            allChecked = false;
            return false;
          }
        });
        allPerm.prop("checked", allChecked);
      });

      // Input validation for number fields
      const numberInputs = document.querySelectorAll('input[type="number"]');
      numberInputs.forEach((input) => {
        input.addEventListener("input", function () {
          this.value = this.value.replace(/[^0-9]/g, "");
        });
      });

      // Enforce length constraints on specific fields
      $("#id_accountnumbershaba").on("input", function () {
        const maxLength = 22;
        if (this.value.length > maxLength) {
          this.value = this.value.slice(0, maxLength);
          $(this).addClass("shake");
          setTimeout(() => {
            $(this).removeClass("shake");
          }, 500);
        }
      });

      $("#id_cardnumber").on("input", function () {
        const maxLength = 16;
        if (this.value.length > maxLength) {
          this.value = this.value.slice(0, maxLength);
          $(this).addClass("shake");
          setTimeout(() => {
            $(this).removeClass("shake");
          }, 500);
        }
      });

      // Add custom styles for validation and animations
      $("head").append(`
                <style>
                    input[type="number"]::-webkit-outer-spin-button,
                    input[type="number"]::-webkit-inner-spin-button {
                        -webkit-appearance: none;
                        margin: 0;
                    }
                    input[type="number"] {
                        -moz-appearance: textfield;
                    }
                    .shake {
                        animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
                    }
                    @keyframes shake {
                        10%, 90% { transform: translate3d(-1px, 0, 0); }
                        20%, 80% { transform: translate3d(2px, 0, 0); }
                        30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
                        40%, 60% { transform: translate3d(3px, 0, 0); }
                    }
                    .highlight {
                        animation: highlight 0.3s ease-in-out;
                    }
                    @keyframes highlight {
                        0% { background-color: transparent; }
                        50% { background-color: rgba(99, 102, 241, 0.2); }
                        100% { background-color: transparent; }
                    }
                </style>
            `);
    }

    function initUserDataHandling() {
      // Parse JSON data from script tags
      const usersData = JSON.parse(
        document.getElementById("users-data").textContent
      );
      const sessionUserData = JSON.parse(
        document.getElementById("session-user-data").textContent
      );
      const contactInfosData = JSON.parse(
        document.getElementById("contact-infos-data").textContent
      );

      // Helper functions for user data management
      function clearFormFields() {
        $("#form")[0].reset();
        $("#id_active").prop("checked", false);
        $("#id_picurl").attr("src", "{% static 'pic/avatar.png' %}");
        $("#accessToExtsDp ul li input").prop("checked", false);
        $(".perm-checkbox").prop("checked", false);
        $("#allPerm").prop("checked", false);

        // Clear all input fields except the username in add mode
        $(
          "form input:not(#id_username, [type=checkbox], [type=radio]), textarea"
        ).val("");
        $("form select").val("");
      }

      function populateUserData(user, contactInfosData) {
        // Basic user info
        $("#id_name").val(user.name);
        $("#id_lastname").val(user.lastname);
        $("#id_extension").val(user.extension);
        $("#id_email").val(user.email);
        $("#id_groupname").val(user.groupname);

        // Set active status
        $("#id_active").prop("checked", user.active);

        // Set profile picture with fade animation
        var picUrl =
          user.picurl === "avatar.png"
            ? "{% static 'pic/avatar.png' %}"
            : "{% static 'upload/' %}" + user.picurl;

        $("#id_picurl").fadeOut(200, function () {
          $(this).attr("src", picUrl).fadeIn(300);
        });

        // Handle user extensions
        var userexts = user.usersextension || [];
        var userExtsTextes = user.getListOfExtsGroups || [];

        if (userexts.length > 0 || userExtsTextes.length > 0) {
          $("#accessToExtsDp ul li input").each(function () {
            var extValue = $(this).val();
            var isChecked =
              userexts.includes(extValue) || userExtsTextes.includes(extValue);
            $(this).prop("checked", isChecked);
          });
        }

        // Handle permissions
        var userPerm = user.getUserCanPerm || {};
        var allTrue = true;

        for (const key in userPerm) {
          if (userPerm[key]) {
            $("#id_" + key).prop("checked", true);
          } else {
            allTrue = false;
            $("#id_" + key).prop("checked", false);
          }
        }

        $("#allPerm").prop("checked", allTrue);

        // Load user info
        const contactInfo = contactInfosData.find(
          (info) => info.username === user.username
        );
        if (contactInfo) {
          $("#id_nationalcode").val(contactInfo.nationalcode);
          $("#id_birthdate").val(contactInfo.birthdate);
          $("#id_telephone").val(contactInfo.telephone);
          $("#id_phonenumber").val(contactInfo.phonenumber);
          $("#id_gender").val(contactInfo.gender);
          $("#id_maritalstatus").val(contactInfo.maritalstatus);
          $("#id_military").val(contactInfo.military);
          $("#id_educationfield").val(contactInfo.educationfield);
          $("#id_educationdegree").val(contactInfo.educationdegree);
          $("#id_province").val(contactInfo.province);
          $("#id_city").val(contactInfo.city);
          $("#id_accountnumbershaba").val(contactInfo.accountnumbershaba);
          $("#id_cardnumber").val(contactInfo.cardnumber);
          $("#id_accountnumber").val(contactInfo.accountnumber);
          $("#id_address").val(contactInfo.address);

          // Trigger gender change for military field visibility
          $("#id_gender").trigger("change");
        }
      }

      // Handle username change in edit mode
      function handleUsernameChange(selectedUsername) {
        if (selectedUsername.toLowerCase() === "none") {
          clearFormFields();
        } else {
          if (
            selectedUsername.toLowerCase() ===
            sessionUserData.username.toLowerCase()
          ) {
            $("#id_label_groupname, #id_groupname").fadeOut(300);
          } else {
            $("#id_label_groupname, #id_groupname").fadeIn(300);
          }

          const userperm = sessionUserData.getUserInfo_groupname;
          if (userperm === "supporter") {
            addOption($("#id_groupname"), "superadmin", "ابر مدیر");
          } else {
            removeOption($("#id_groupname"), "superadmin");
          }

          const user = usersData.find(
            (u) => u.username.toLowerCase() === selectedUsername.toLowerCase()
          );
          if (user) {
            populateUserData(user, contactInfosData);
          }
        }
      }

      // Utility functions
      function addOption(selectElement, value, text) {
        if (selectElement.find("option[value='" + value + "']").length === 0) {
          selectElement.append(
            $("<option></option>").attr("value", value).text(text)
          );
        }
      }

      function removeOption(selectElement, value) {
        selectElement.find("option[value='" + value + "']").remove();
      }

      // Handle edit/add toggle
      $("#id_editOrAdd").change(function () {
        var select_edit = $(this).val().toLowerCase();
        if (select_edit == "edit") {
          const usernameSelectHtml = `
                    <select id="id_username" name="username" class="form-control">
                        <option value='none'> ------ </option>
                        ${usersData
                          .map(
                            (user) =>
                              `<option value='${user.username}'> ${user.username} </option>`
                          )
                          .join("")}
                    </select>`;

          $("#id_username").fadeOut(200, function () {
            $(this).replaceWith(usernameSelectHtml);
            $("#id_username")
              .fadeIn(300)
              .on("change", function () {
                handleUsernameChange($(this).val());
              });
          });
        } else {
          $("#id_username").fadeOut(200, function () {
            $(this).replaceWith(`
                        <input type="text" id="id_username" name='username' class="form-control">`);
            $("#id_username").fadeIn(300);
          });
          clearFormFields();
        }
      });

      // Initial call to handle the default state
      $("#id_editOrAdd").trigger("change");
    }

    // Add these custom styles for better interactions
    $("head").append(`
            <style>
                input[type="number"]::-webkit-outer-spin-button,
                input[type="number"]::-webkit-inner-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
                input[type="number"] {
                    -moz-appearance: textfield;
                }
                .shake {
                    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
                }
                @keyframes shake {
                    10%, 90% { transform: translate3d(-1px, 0, 0); }
                    20%, 80% { transform: translate3d(2px, 0, 0); }
                    30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
                    40%, 60% { transform: translate3d(3px, 0, 0); }
                }
                .highlight {
                    animation: highlight 0.3s ease-in-out;
                }
                @keyframes highlight {
                    0% { background-color: transparent; }
                    50% { background-color: rgba(138, 125, 234, 0.2); }
                    100% { background-color: transparent; }
                }
                
                /* Add ripple effect */
                .btn, .action-button, .nav-tab {
                    position: relative;
                    overflow: hidden;
                }
                .ripple {
                    position: absolute;
                    width: 10px;
                    height: 10px;
                    background-color: rgba(255, 255, 255, 0.4);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                }
                @keyframes ripple {
                    to {
                        transform: scale(20);
                        opacity: 0;
                    }
                }
                
                /* Add pulse animation */
                .pulse {
                    animation: pulse 0.5s ease-in-out;
                }
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
                
                /* Add floating animation */
                @keyframes float {
                    0% { transform: translateY(0); }
                    50% { transform: translateY(-8px); }
                    100% { transform: translateY(0); }
                }
                
                .feature-card:hover .icon {
                    animation: float 2s ease-in-out infinite;
                }
            </style>
        `);

    // Apply ripple effect to buttons
    $(".btn, .action-button, .nav-tab").on("click", function (e) {
      const button = $(this);

      // Create ripple element
      const ripple = $('<span class="ripple"></span>');
      button.append(ripple);

      // Set ripple position
      const buttonPos = button.offset();
      const rippleX = e.pageX - buttonPos.left;
      const rippleY = e.pageY - buttonPos.top;

      ripple.css({
        top: rippleY + "px",
        left: rippleX + "px",
      });

      // Remove ripple after animation completes
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });

    // Add hover effects to feature cards
    $(".feature-card")
      .on("mouseenter", function () {
        $(this).css({
          transform: "translateY(-5px)",
          "box-shadow": "var(--shadow-lg)",
          transition: "all 0.3s ease",
        });
      })
      .on("mouseleave", function () {
        $(this).css({
          transform: "translateY(0)",
          "box-shadow": "var(--shadow)",
          transition: "all 0.3s ease",
        });
      });
  });
</script>
{% endblock %}
