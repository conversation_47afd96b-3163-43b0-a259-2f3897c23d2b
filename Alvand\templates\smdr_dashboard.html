{% extends "base.html" %}
{% load static %}

{% block head_extra %}
<!-- <link rel="stylesheet" href="{% static 'css/base-template-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/rtl-fixes.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-header-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/search-input-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/rtl-dashboard-layout.css' %}" />
<link rel="stylesheet" href="{% static 'css/nav-rtl-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dropdown-rtl-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/sidebar-position-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-content-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/sidebar-visual-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-width-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/header-width-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-spacing-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/theme-toggle-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-center-fix.css' %}" /> -->
<style>
    /* Override any existing styles with RTL-specific styles */
    body {
        direction: rtl;
        text-align: right;
    }
    
    :root {
        --sidebar-width: 135px;
        --main-padding: 1rem;
        --text-color: #000;
        --bg-color: #fff;
        --border-color: #e2e8f0;
    }
    
    html.dark {
        --text-color: #fff !important;
        --bg-color: #1a202c !important;
        --border-color: #4a5568 !important;
    }
    
    .main-content {
        margin-right: var(--sidebar-width);
        margin-left: 0;
        width: calc(100% - var(--sidebar-width));
        padding: var(--main-padding);
    }
    
    /* RTL Support */
    html[dir="ltr"] .main-content {
        margin-right: 0;
        margin-left: var(--sidebar-width);
    }
    
    @media (max-width: 768px) {
        .main-content {
            margin-right: 0;
            margin-left: 0;
            width: 100%;
        }
    }
    
    .table-container {
        direction: rtl;
    }
    
    table {
        direction: rtl;
    }
    
    th, td {
        text-align: right !important;
    }
    
    /* Force table columns to be in correct RTL order */
    .custom-table th,
    .custom-table td {
        direction: rtl;
        text-align: right !important;
    }
    
    /* Fix for sticky positioning in RTL */
    .th-fixed {
        position: sticky;
        right: 0;
        background-color: #f8fafc;
        z-index: 10;
    }
    
    .td-fixed {
        position: sticky;
        right: 0;
        background-color: white;
        z-index: 10;
    }
    
    /* Add border right for better visual separation */
    .td-fixed {
        border-left: 1px solid #e5e7eb !important;
    }
</style>
{% endblock %}

{% block main %}

<div class="min-h-screen bg-gradient-to-b from-slate-50 to-slate-100">
    <!-- Sidebar (mobile drawer) -->
    <div id="dashboardDrawer" class="fixed inset-0 z-40 transform -translate-x-full transition-transform duration-300 ease-in-out lg:hidden">
        <div class="absolute inset-0 bg-black bg-opacity-50" id="drawerBackdrop"></div>
        <div class="relative w-64 max-w-[80%] h-full bg-white shadow-xl flex flex-col rtl">
            <div class="p-4 border-b border-slate-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold text-blue-600">لوتوس</h2>
                    <button id="closeDrawer" class="text-slate-500 hover:text-slate-700">
                        <i class="fa-solid fa-xmark text-xl"></i>
                    </button>
                </div>
            </div>
            <nav class="flex-1 p-4 overflow-y-auto">
                <ul class="space-y-2">
                    <li>
                        <a href="{% url 'dashboard-improved' %}" class="flex items-center gap-2 px-4 py-2.5 rounded-lg text-slate-600 hover:bg-slate-50 font-medium">
                            <i class="fa-solid fa-gauge-high"></i>
                            <span>داشبورد</span>
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'smdr-dashboard' %}" class="flex items-center gap-2 px-4 py-2.5 rounded-lg bg-blue-50 text-blue-600 font-medium">
                            <i class="fa-solid fa-phone"></i>
                            <span>داشبورد SMDR</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center gap-2 px-4 py-2.5 rounded-lg text-slate-600 hover:bg-slate-50 font-medium">
                            <i class="fa-solid fa-user-group"></i>
                            <span>مخاطبین</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center gap-2 px-4 py-2.5 rounded-lg text-slate-600 hover:bg-slate-50 font-medium">
                            <i class="fa-solid fa-chart-line"></i>
                            <span>گزارشات</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center gap-2 px-4 py-2.5 rounded-lg text-slate-600 hover:bg-slate-50 font-medium">
                            <i class="fa-solid fa-gear"></i>
                            <span>تنظیمات</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex flex-col lg:flex-row rtl">
        <!-- Sidebar (desktop) -->
        <aside class="hidden lg:block w-64 min-h-screen bg-white shadow-sm border-l border-slate-200">
            <div class="p-6 border-b border-slate-200">
                <h2 class="text-2xl font-bold text-blue-600">لوتوس</h2>
            </div>
            <nav class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="{% url 'dashboard-improved' %}" class="flex items-center gap-2 px-4 py-2.5 rounded-lg text-slate-600 hover:bg-slate-50 font-medium">
                            <i class="fa-solid fa-gauge-high"></i>
                            <span>داشبورد</span>
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'smdr-dashboard' %}" class="flex items-center gap-2 px-4 py-2.5 rounded-lg bg-blue-50 text-blue-600 font-medium">
                            <i class="fa-solid fa-phone"></i>
                            <span>داشبورد SMDR</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center gap-2 px-4 py-2.5 rounded-lg text-slate-600 hover:bg-slate-50 font-medium">
                            <i class="fa-solid fa-user-group"></i>
                            <span>مخاطبین</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center gap-2 px-4 py-2.5 rounded-lg text-slate-600 hover:bg-slate-50 font-medium">
                            <i class="fa-solid fa-chart-line"></i>
                            <span>گزارشات</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center gap-2 px-4 py-2.5 rounded-lg text-slate-600 hover:bg-slate-50 font-medium">
                            <i class="fa-solid fa-gear"></i>
                            <span>تنظیمات</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="flex-1 min-h-screen">
            <!-- Navbar -->
            <header class="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-30">
                <div class="mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex items-center justify-between h-16">
                        <!-- Left side (Mobile menu & title) -->
                        <div class="flex items-center gap-4">
                            <button id="openDrawer" class="lg:hidden p-2 rounded-lg text-slate-500 hover:bg-slate-100">
                                <i class="fa-solid fa-bars"></i>
                            </button>
                            <div>
                                <h1 class="text-xl font-bold text-slate-800 lg:hidden">داشبورد SMDR</h1>
                            </div>
                        </div>

                        <!-- Right side (User & Notifications) -->
                        <div class="flex items-center gap-3">
                            <button class="relative p-2 rounded-full text-slate-500 hover:bg-slate-100">
                                <i class="fa-regular fa-bell"></i>
                                <span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                            </button>
                            <div class="relative">
                                <button id="userMenuBtn" class="flex items-center gap-2 p-1 rounded-full hover:bg-slate-100">
                                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
                                        <i class="fa-solid fa-user"></i>
                                    </div>
                                    <span class="hidden md:inline-block text-sm font-medium text-slate-700">نام کاربر</span>
                                    <i class="fa-solid fa-chevron-down text-xs text-slate-400"></i>
                                </button>
                                <div id="userMenuDropdown" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-10 border border-slate-200">
                                    <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">پروفایل</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-50">تنظیمات</a>
                                    <div class="border-t border-slate-200 my-1"></div>
                                    <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-slate-50">خروج</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="py-6 px-4 sm:px-6 lg:px-8">
                <!-- Dashboard Header -->
                <div class="mb-6">
                    <div class="flex flex-wrap items-center justify-between gap-4">
                        <div>
                            <h1 class="text-2xl font-bold text-slate-800 hidden lg:block rtl">داشبورد SMDR</h1>
                            <p class="text-sm text-slate-500 rtl">گزارش تماس‌های SMDR</p>
                        </div>
                        <div class="flex items-center gap-3">
                            <span class="hidden sm:inline-block px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                                امروز: {% now "Y/m/d" %}
                            </span>
                            <button type="button" id="printbtn" class="px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-lg text-sm font-medium transition-colors focus:ring-2 focus:ring-violet-500 focus:ring-offset-2 shadow-sm">
                                <i class="fa-solid fa-print mr-1"></i> چاپ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                    <!-- Total Calls -->
                    <div class="bg-white rounded-xl shadow-sm p-5 border border-slate-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-slate-500">تعداد کل تماس‌ها</p>
                                <h3 class="text-2xl font-bold text-slate-800 mt-1">{{ total_count|default:"0" }}</h3>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center text-blue-500">
                                <i class="fa-solid fa-phone text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Incoming Calls -->
                    <div class="bg-white rounded-xl shadow-sm p-5 border border-slate-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-slate-500">تماس‌های ورودی</p>
                                <h3 class="text-2xl font-bold text-slate-800 mt-1">{{ incoming_count|default:"0" }}</h3>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center text-green-500">
                                <i class="fa-solid fa-phone-arrow-down-left text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Outgoing Calls -->
                    <div class="bg-white rounded-xl shadow-sm p-5 border border-slate-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-slate-500">تماس‌های خروجی</p>
                                <h3 class="text-2xl font-bold text-slate-800 mt-1">{{ outgoing_count|default:"0" }}</h3>
                            </div>
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center text-yellow-500">
                                <i class="fa-solid fa-phone-arrow-up-right text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Internal Calls -->
                    <div class="bg-white rounded-xl shadow-sm p-5 border border-slate-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-slate-500">تماس‌های داخلی</p>
                                <h3 class="text-2xl font-bold text-slate-800 mt-1">{{ internal_count|default:"0" }}</h3>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center text-purple-500">
                                <i class="fa-solid fa-building text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- System Messages -->
                    <div class="bg-white rounded-xl shadow-sm p-5 border border-slate-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-slate-500">پیام‌های سیستم</p>
                                <h3 class="text-2xl font-bold text-slate-800 mt-1">{{ system_count|default:"0" }}</h3>
                            </div>
                            <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center text-indigo-500">
                                <i class="fa-solid fa-server text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters & Search -->
                <div class="bg-white rounded-xl shadow-sm p-5 border border-slate-100 mb-6">
                    <form method="get" class="rtl">
                        <!-- Filters Section -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                            <!-- Call Type Filter -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">نوع تماس</label>
                                <div class="relative">
                                    <select name="call_type" multiple class="block w-full rounded-md border-slate-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-3 py-2 text-slate-700">
                                        <option value="همه تماس ها" {% if 'همه تماس ها' in request.GET.getlist "call_type" %}selected{% endif %}>همه تماس ها</option>
                                        <option value="تماس های ورودی" {% if 'تماس های ورودی' in request.GET.getlist "call_type" %}selected{% endif %}>تماس های ورودی</option>
                                        <option value="تماس های خروجی" {% if 'تماس های خروجی' in request.GET.getlist "call_type" %}selected{% endif %}>تماس های خروجی</option>
                                        <option value="تماس های داخلی" {% if 'تماس های داخلی' in request.GET.getlist "call_type" %}selected{% endif %}>تماس های داخلی</option>
                                        <option value="پیام های سیستم" {% if 'پیام های سیستم' in request.GET.getlist "call_type" %}selected{% endif %}>پیام های سیستم</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Extension Filter -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">داخلی</label>
                                <div class="relative">
                                    <select name="extension" multiple class="block w-full rounded-md border-slate-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-3 py-2 text-slate-700">
                                        {% for ext in extensions %}
                                            <option value="{{ ext }}" {% if ext in request.GET.getlist "extension" %}selected{% endif %}>{{ ext }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- CO (Urban Line) Filter -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">خط شهری</label>
                                <div class="relative">
                                    <select name="co" multiple class="block w-full rounded-md border-slate-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-3 py-2 text-slate-700">
                                        {% for co in cos %}
                                            <option value="{{ co }}" {% if co in request.GET.getlist "co" %}selected{% endif %}>{{ co }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- Search -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">جستجو</label>
                                <div class="relative flex items-center">
                                    <i class="fa-solid fa-magnifying-glass text-slate-400 absolute right-3 z-10"></i>
                                    <input type="text" name="search" placeholder="جستجو شماره..." class="block w-full rounded-md border-slate-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 pr-10 pl-3 py-2 text-slate-700" value="{{ request.GET.search|default:'' }}">
                                </div>
                            </div>
                        </div>

                        <!-- Date Range -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">از تاریخ</label>
                                <div class="relative flex items-center">
                                    <i class="fa-regular fa-calendar text-slate-400 absolute right-3 z-10"></i>
                                    <input type="date" name="dateFrom" class="block w-full rounded-md border-slate-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 pr-10 pl-3 py-2 text-slate-700" value="{{ request.GET.dateFrom|default:'' }}">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">تا تاریخ</label>
                                <div class="relative flex items-center">
                                    <i class="fa-regular fa-calendar text-slate-400 absolute right-3 z-10"></i>
                                    <input type="date" name="dateTo" class="block w-full rounded-md border-slate-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 pr-10 pl-3 py-2 text-slate-700" value="{{ request.GET.dateTo|default:'' }}">
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end gap-2">
                            <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors">
                                <i class="fa-solid fa-filter mr-1"></i> اعمال فیلتر
                            </button>
                            <a href="{% url 'smdr-dashboard' %}" class="px-4 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-lg text-sm font-medium transition-colors">
                                پاک کردن
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Records Table -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden border border-slate-100 mb-6">
                    <div class="overflow-x-auto table-container">
                        <table class="w-full rtl text-sm custom-table" dir="rtl" style="direction: rtl;">
                            <thead class="bg-slate-50 text-slate-700">
                                <tr class="border-b border-slate-200">
                                    <th class="px-4 py-3 text-right font-medium" style="text-align: right !important;">مدت</th>
                                    <th class="px-4 py-3 text-right font-medium" style="text-align: right !important;">نوع تماس</th>
                                    <th class="px-4 py-3 text-right font-medium" style="text-align: right !important;">شماره</th>
                                    <th class="px-4 py-3 text-right font-medium" style="text-align: right !important;">خط شهری</th>
                                    <th class="px-4 py-3 text-right font-medium" style="text-align: right !important;">داخلی</th>
                                    <th class="px-4 py-3 text-right font-medium" style="text-align: right !important;">زمان</th>
                                    <th class="px-4 py-3 text-right font-medium" style="text-align: right !important;">تاریخ</th>
                                    <th class="px-4 py-3 text-right font-medium th-fixed" style="text-align: right !important;">ردیف</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in dashPage %}
                                <tr class="border-b border-slate-100 hover:bg-slate-50">
                                    <td class="px-4 py-3 text-right text-slate-700" style="text-align: right !important;">{{ record.duration|default:"-" }}</td>
                                    <td class="px-4 py-3 text-right" style="text-align: right !important;">
                                        {% if record.is_incoming %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                ورودی
                                            </span>
                                        {% elif record.is_outgoing %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                خروجی
                                            </span>
                                        {% elif record.is_internal %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                داخلی
                                            </span>
                                        {% elif record.is_system_message %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                سیستم
                                            </span>
                                        {% else %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                                                نامشخص
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-4 py-3 text-right text-slate-700" style="text-align: right !important;">{{ record.dial_number|default:"-" }}</td>
                                    <td class="px-4 py-3 text-right text-slate-700" style="text-align: right !important;">{{ record.co|default:"-" }}</td>
                                    <td class="px-4 py-3 text-right text-slate-700" style="text-align: right !important;">{{ record.ext|default:"-" }}</td>
                                    <td class="px-4 py-3 text-right text-slate-700" style="text-align: right !important;">{{ record.time|time:"H:i" }}</td>
                                    <td class="px-4 py-3 text-right text-slate-700" style="text-align: right !important;">{{ record.date|date:"Y/m/d" }}</td>
                                    <td class="px-4 py-3 text-right text-slate-700 td-fixed" style="text-align: right !important;">{{ forloop.counter }}</td>
                                </tr>
                                {% empty %}
                                <tr class="border-b border-slate-100">
                                    <td colspan="8" class="px-4 py-8 text-center text-slate-500">هیچ رکوردی یافت نشد.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                {% if dashPage.paginator.num_pages > 1 %}
                <div class="flex justify-center mt-6">
                    <div class="flex border border-slate-300 rounded-md overflow-hidden">
                        {% if dashPage.has_previous %}
                        <a href="?p={{ dashPage.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'p' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="px-3 py-2 bg-white hover:bg-slate-50 text-slate-700 text-sm border-r border-slate-300">
                            <i class="fa-solid fa-chevron-right mr-1"></i> قبلی
                        </a>
                        {% endif %}
                        
                        <div class="px-3 py-2 bg-white text-slate-700 text-sm border-r border-slate-300">
                            <span>صفحه {{ dashPage.number }} از {{ dashPage.paginator.num_pages }}</span>
                        </div>
                        
                        {% if dashPage.has_next %}
                        <a href="?p={{ dashPage.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'p' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="px-3 py-2 bg-white hover:bg-slate-50 text-slate-700 text-sm">
                            بعدی <i class="fa-solid fa-chevron-left ml-1"></i>
                        </a>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile drawer functionality
    const openDrawerBtn = document.getElementById('openDrawer');
    const closeDrawerBtn = document.getElementById('closeDrawer');
    const drawer = document.getElementById('dashboardDrawer');
    const drawerBackdrop = document.getElementById('drawerBackdrop');
    
    if (openDrawerBtn) {
        openDrawerBtn.addEventListener('click', function() {
            drawer.classList.remove('-translate-x-full');
        });
    }
    
    if (closeDrawerBtn) {
        closeDrawerBtn.addEventListener('click', function() {
            drawer.classList.add('-translate-x-full');
        });
    }
    
    if (drawerBackdrop) {
        drawerBackdrop.addEventListener('click', function() {
            drawer.classList.add('-translate-x-full');
        });
    }
    
    // User menu dropdown
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenuDropdown = document.getElementById('userMenuDropdown');
    
    if (userMenuBtn && userMenuDropdown) {
        userMenuBtn.addEventListener('click', function() {
            userMenuDropdown.classList.toggle('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!userMenuBtn.contains(event.target) && !userMenuDropdown.contains(event.target)) {
                userMenuDropdown.classList.add('hidden');
            }
        });
    }
    
    // Print functionality
    const printBtn = document.getElementById('printbtn');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            window.print();
        });
    }

    // Ensure RTL display for tables
    document.querySelectorAll('.custom-table').forEach(function(table) {
        // Force RTL direction for all table elements
        table.style.direction = 'rtl';
        table.setAttribute('dir', 'rtl');
        
        // Set all cells to align right
        table.querySelectorAll('th, td').forEach(function(cell) {
            cell.style.textAlign = 'right';
        });
    });
});
</script>

<script src="{% static 'js/remove-theme-toggle.js' %}"></script>

{% endblock %} 