# Generated by Django 5.1.6 on 2025-04-02 12:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Alvand', '0026_lices'),
    ]

    operations = [
        migrations.CreateModel(
            name='externalDBInfos',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip', models.CharField(max_length=100)),
                ('port', models.IntegerField(default=5432)),
                ('username', models.CharField(max_length=100)),
                ('database', models.Char<PERSON>ield(max_length=100)),
                ('password', models.<PERSON>r<PERSON><PERSON>(max_length=200)),
            ],
        ),
    ]
