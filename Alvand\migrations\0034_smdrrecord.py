# Generated by Django 5.1.6 on 2025-05-15 08:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Alvand', '0033_rename_cabletype_device_cable_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='SMDRRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='تاریخ')),
                ('time', models.TimeField(verbose_name='زمان')),
                ('ext', models.CharField(blank=True, max_length=50, null=True, verbose_name='داخلی')),
                ('co', models.CharField(blank=True, max_length=50, null=True, verbose_name='خط شهری')),
                ('dial_number', models.CharField(blank=True, max_length=200, null=True, verbose_name='شماره گرفته شده')),
                ('ring_time', models.Char<PERSON>ield(blank=True, max_length=50, null=True, verbose_name='زمان زنگ')),
                ('duration', models.Char<PERSON>ield(blank=True, max_length=50, null=True, verbose_name='مدت تماس')),
                ('acc_code', models.CharField(blank=True, max_length=50, null=True, verbose_name='کد دسترسی')),
                ('cd_code', models.CharField(blank=True, max_length=50, null=True, verbose_name='کد CD')),
                ('call_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع تماس')),
                ('is_internal', models.BooleanField(default=False, verbose_name='تماس داخلی')),
                ('is_incoming', models.BooleanField(default=False, verbose_name='تماس ورودی')),
                ('is_outgoing', models.BooleanField(default=False, verbose_name='تماس خروجی')),
                ('is_system_message', models.BooleanField(default=False, verbose_name='پیام سیستمی')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'گزارش SMDR',
                'verbose_name_plural': 'گزارش های SMDR',
                'ordering': ['-date', '-time'],
            },
        ),
    ]
