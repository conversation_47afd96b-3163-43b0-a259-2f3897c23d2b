{% extends "base.html" %}
{% load static %}
{% block main %}

<link rel="stylesheet" href="{% static 'css/dashboard-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-width-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/direct-layout-fix.css' %}" />
<link rel="stylesheet" href="{% static 'css/dashboard-content.css' %}" />

<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    
    :root {
        /* Match theme.css variables */
        --primary-color: #00BCD4;       /* Lotus Aqua */
        --primary-hover: #29B6F6;       /* Sky Blue */
        --secondary-color: #F5F7FA;     /* White Smoke */
        --text-color: #37474F;          /* Dark Gray */
        --light-text: #90A4AE;          /* Medium Gray */
        --border-color: #CFD8DC;        /* Light Gray */
        --bg-color: #F5F7FA;            /* White Smoke */
        --card-bg: white;
        --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        
        /* Dashboard-specific variables */
        --primary: var(--primary-color);
        --primary-dark: #0097A7;        /* Darker Aqua */
        --primary-light: #80DEEA;       /* Soft Mint */
        --secondary: #26A69A;           /* Emerald Green */
        --warning: #FF7043;             /* Sunset Orange */
        --danger: #FF5252;              /* Bright Red */
        --light: #F5F7FA;               /* White Smoke */
        --dark: #0D1B2A;                /* Deep Indigo */
        --gray: #90A4AE;                /* Medium Gray */
        --gray-light: #CFD8DC;          /* Light Gray */
        --transition: all 0.3s ease;
    }
    
    /* Dark mode variables to match theme.css */
    html.dark {
        --primary-color: #00BCD4;         /* Lotus Aqua */
        --primary-hover: #29B6F6;         /* Sky Blue */
        --secondary-color: #0D1B2A;       /* Deep Indigo */
        --text-color: #F5F7FA;            /* White Smoke */
        --light-text: #CFD8DC;            /* Light Gray */
        --border-color: #37474F;          /* Dark Gray */
        --bg-color: #121212;              /* Dark Navy */
        --card-bg: #0D1B2A;               /* Deep Indigo */
        --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        
        /* Update dashboard-specific dark mode variables */
        --primary: var(--primary-color);
        --primary-dark: var(--primary-hover);
        --light: var(--text-color);
        --dark: var(--bg-color);
        --gray: var(--light-text);
        --gray-light: var(--border-color);
    }
    
    /* Fix for theme toggle icons in navbar */
    .theme-toggle-btn i,
    .dark-mode-toggle i,
    .light-mode-toggle i,
    [data-theme-toggle] i,
    .theme-switch i,
    #themeToggle i,
    .navbar .fa-moon,
    .navbar .fa-sun,
    header .fa-moon,
    header .fa-sun,
    .nav .fa-moon,
    .nav .fa-sun,
    .theme-toggle-icon {
        color: #00BCD4 !important;     /* Lotus Aqua */
        font-size: 1.25rem !important;
        filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2)) !important;
        opacity: 1 !important;
        visibility: visible !important;
        transition: all 0.2s ease !important;
    }
    
    .dark .theme-toggle-btn i,
    .dark .dark-mode-toggle i,
    .dark .light-mode-toggle i,
    .dark [data-theme-toggle] i,
    .dark .theme-switch i,
    .dark #themeToggle i,
    .dark .navbar .fa-moon,
    .dark .navbar .fa-sun,
    .dark header .fa-moon,
    .dark header .fa-sun,
    .dark .nav .fa-moon, 
    .dark .nav .fa-sun,
    .dark .theme-toggle-icon {
        color: #80DEEA !important;    /* Soft Mint */
    }
    
    /* Specific styles for sun/moon icons to ensure visibility */
    .fa-moon {
        color: #00BCD4 !important;    /* Lotus Aqua */
    }
    
    .dark .fa-moon {
        color: #80DEEA !important;    /* Soft Mint */
    }
    
    .fa-sun {
        color: #FF7043 !important;    /* Sunset Orange */
    }
    
    .dark .fa-sun {
        color: #FF9E80 !important;    /* Lighter Sunset Orange */
    }
    
    /* Show correct icon based on theme */
    .fa-sun {
        display: none !important;
    }
    
    .fa-moon {
        display: inline-block !important;
    }
    
    .dark .fa-sun {
        display: inline-block !important;
    }
    
    .dark .fa-moon {
        display: none !important;
    }
    
    /* Base RTL Layout */
    body {
        direction: rtl;
        text-align: right;
        font-family: 'Vazir', system-ui, -apple-system, sans-serif;
        color: var(--text-color);
        background-color: var(--bg-color);
        min-height: 100vh;
        line-height: 1.6;
        transition: background-color 0.3s, color 0.3s;
    }

    /* Enhanced Support Page Styles */
    .support-container {
        max-width: 1280px;
        margin: 0 auto;
        padding: 2rem 1.5rem;
    }

    /* Modern Hero Section with Particles */
    .support-hero {
        background: linear-gradient(135deg, #00BCD4 0%, #536DFE 100%); /* Lotus Aqua to Electric Violet */
        padding: 3rem 1rem;
        position: relative;
        overflow: hidden;
        border-radius: 1rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 188, 212, 0.2); /* Lotus Aqua with opacity */
    }

    .particles-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 1;
    }

    .particle {
        position: absolute;
        width: 6px;
        height: 6px;
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 50%;
        animation: float 20s infinite ease-in-out;
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0) translateX(0);
            opacity: 0;
        }
        50% {
            transform: translateY(-100px) translateX(100px);
            opacity: 0.8;
        }
    }

    .support-hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
        color: white;
    }

    .support-hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1.5rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        background: linear-gradient(135deg, #ffffff 30%, #e2e8f0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transform: translateY(0);
        animation: titleFloat 6s ease-in-out infinite;
    }

    @keyframes titleFloat {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
    }

    .support-hero-subtitle {
        font-size: 1.5rem;
        opacity: 0.95;
        margin-bottom: 2.5rem;
        font-weight: 400;
    }

    .support-hero-button {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        background: rgba(255, 255, 255, 0.95);
        color: #4f46e5;
        padding: 1.2rem 3rem;
        border-radius: 9999px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        text-decoration: none;
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
    }

    .support-hero-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transform: translateX(-100%);
    }

    .support-hero-button:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        background: white;
    }

    .support-hero-button:hover::before {
        animation: shimmerButton 1.5s infinite;
    }

    @keyframes shimmerButton {
        100% { transform: translateX(100%); }
    }

    /* Modern Card Design */
    .support-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 2.5rem;
    }

    @media (min-width: 1024px) {
        .support-grid {
            grid-template-columns: 1fr 1fr;
        }
    }

    .support-card {
        background: var(--card-bg);
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
        border: 1px solid var(--border-color);
        position: relative;
    }

    .support-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .support-card-content {
        padding: 2.5rem;
    }

    /* Styled Section Headers */
    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 2.5rem;
        position: relative;
    }

    .section-header::after {
        content: '';
        position: absolute;
        bottom: -1rem;
        right: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), transparent);
        border-radius: 3px;
    }

    .section-icon-wrapper {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        padding: 1.2rem;
        border-radius: 1rem;
        margin-left: 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 10px 20px rgba(99, 102, 241, 0.2);
    }

    .section-icon {
        color: white;
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-color);
        position: relative;
    }

    /* Enhanced About Content */
    .about-content p {
        font-size: 1.1rem;
        line-height: 1.8;
        color: var(--light-text);
        margin-bottom: 1.5rem;
    }

    .highlight-text {
        font-weight: 700;
        color: var(--primary-color);
        position: relative;
        display: inline-block;
    }

    .highlight-text::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), transparent);
        border-radius: 2px;
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.3s ease;
    }

    .highlight-text:hover::after {
        transform: scaleX(1);
        transform-origin: left;
    }

    .quote-box {
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(99, 102, 241, 0.1));
        padding: 2.5rem;
        border-radius: 1rem;
        border-left: 5px solid var(--primary-color);
        margin: 2.5rem 0;
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.03);
    }

    .quote-box::before {
        content: '"';
        position: absolute;
        top: -1.5rem;
        right: 1.5rem;
        font-size: 6rem;
        color: var(--primary-color);
        opacity: 0.2;
        font-family: serif;
    }

    .quote-text {
        color: var(--text-color);
        font-style: italic;
        font-size: 1.3rem;
        font-weight: 500;
        line-height: 1.8;
    }

    .announcement-box {
        text-align: center;
        margin-top: 3rem;
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(124, 58, 237, 0.1));
        padding: 2.5rem;
        border-radius: 1rem;
        border: 1px solid rgba(99, 102, 241, 0.2);
        position: relative;
        overflow: hidden;
    }

    .announcement-box::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
        animation: pulse 3s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(0.8); opacity: 0.5; }
        50% { transform: scale(1); opacity: 0.8; }
        100% { transform: scale(0.8); opacity: 0.5; }
    }

    .announcement-text {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
        position: relative;
        z-index: 1;
    }

            /* Enhanced Lotus Design */
    .company-image {
        height: 15rem;
        background: linear-gradient(135deg, #00BCD4 0%, #536DFE 100%); /* Lotus Aqua to Electric Violet */
        position: relative;
        overflow: hidden;
        display: flex;
    }
    
    /* Tech pattern overlay for Lotus section */
    .tech-pattern {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: radial-gradient(rgba(255, 255, 255, 0.2) 2px, transparent 2px);
        background-size: 30px 30px;
        opacity: 0.3;
        pointer-events: none;
    }
    
    /* Add wave animation to company-image */
    .company-image::before,
    .company-image::after {
        content: '';
        position: absolute;
        left: 0;
        width: 200%;
        height: 10px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        animation: wave 8s infinite linear;
    }
    
    .company-image::before {
        bottom: 50px;
        opacity: 0.5;
    }
    
    .company-image::after {
        bottom: 25px;
        animation-delay: 2s;
        animation-duration: 12s;
    }
    
    @keyframes wave {
        0% { transform: translateX(0); }
        100% { transform: translateX(-50%); }
    }
    
    .company-image {
        align-items: center;
        justify-content: center;
    }

    .lotus-logo {
        width: 120px;
        height: 120px;
        display: block;
        margin: 0 auto;
        position: relative;
        z-index: 5;
    }
    
    /* Add animated glow effect to the logo */
    .lotus-logo::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 150px;
        height: 150px;
        background: radial-gradient(circle, rgba(0, 188, 212, 0.6) 0%, rgba(83, 109, 254, 0.3) 40%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        z-index: -1;
        animation: pulse-glow 3s infinite ease-in-out;
    }
    
    @keyframes pulse-glow {
        0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
        50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.2); }
    }

    /* Enhanced Contact Items */
    .contact-methods {
        display: grid;
        gap: 1.5rem;
    }

    .contact-item {
        background: linear-gradient(135deg, var(--card-bg), rgba(99, 102, 241, 0.02));
        border-radius: 1rem;
        padding: 1.5rem;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .contact-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(180deg, var(--primary-color), var(--primary-hover));
    }

    .contact-item:hover {
        transform: translateX(5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        border-color: var(--primary-color);
    }

    .contact-icon-wrapper {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        padding: 1rem;
        border-radius: 0.75rem;
        color: white;
        margin-left: 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 10px 20px rgba(99, 102, 241, 0.2);
    }

    .contact-item:hover .contact-icon-wrapper {
        transform: rotate(15deg) scale(1.1);
    }

    .contact-details {
        flex: 1;
    }

    .contact-details h4 {
        font-size: 0.9rem;
        color: var(--light-text);
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .contact-details a {
        color: var(--primary-color);
        font-weight: 600;
        text-decoration: none;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        position: relative;
        display: inline-block;
    }

    .contact-details a::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), transparent);
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.3s ease;
    }

    .contact-details a:hover {
        color: var(--primary-hover);
    }

    .contact-details a:hover::after {
        transform: scaleX(1);
        transform-origin: left;
    }

    /* Modern Form Design */
    .support-form {
        margin-top: 3rem;
        padding-top: 2.5rem;
        border-top: 1px solid var(--border-color);
        position: relative;
    }

    .support-form::before {
        content: '';
        position: absolute;
        top: -1px;
        right: 0;
        width: 30%;
        height: 3px;
        background: linear-gradient(90deg, transparent, var(--primary-color));
    }

    .form-group {
        margin-bottom: 2rem;
        position: relative;
    }

    .form-label {
        display: block;
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-input-wrapper {
        position: relative;
    }

    .form-input, .form-textarea {
        width: 100%;
        padding: 1.25rem 1.25rem 1.25rem 3rem;
        border: 2px solid var(--border-color);
        border-radius: 0.75rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--card-bg);
        color: var(--text-color);
        font-family: inherit;
    }

    .form-input:focus, .form-textarea:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 5px rgba(99, 102, 241, 0.1);
        outline: none;
    }

    .form-textarea {
        resize: vertical;
        min-height: 10rem;
    }

    .form-icon {
        position: absolute;
        right: 1.25rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--light-text);
        transition: all 0.3s ease;
        font-size: 1.25rem;
    }

    .form-input:focus + .form-icon,
    .form-textarea:focus + .form-icon {
        color: var(--primary-color);
        transform: translateY(-50%) scale(1.2);
    }

    .form-textarea + .form-icon {
        top: 1.5rem;
    }

    .support-button {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        color: white;
        padding: 1.25rem 2rem;
        border-radius: 0.75rem;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        width: 100%;
        box-shadow: 0 10px 20px rgba(99, 102, 241, 0.2);
        position: relative;
        overflow: hidden;
    }

    .support-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transform: translateX(-100%);
    }

    .support-button:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(99, 102, 241, 0.3);
    }

    .support-button:hover::before {
        animation: shimmerButton 1.5s infinite;
    }

    .support-button:active {
        transform: translateY(-2px);
    }

    /* Restore missing dark mode styles */
    html.dark .form-input, 
    html.dark .form-textarea {
        background-color: rgba(31, 41, 55, 0.8);
        border-color: var(--border-color);
    }

    html.dark .form-input:focus, 
    html.dark .form-textarea:focus {
        background-color: rgba(31, 41, 55, 1);
        border-color: var(--primary-color);
    }

    html.dark .contact-item,
    html.dark .quote-box,
    html.dark .announcement-box {
        background: linear-gradient(135deg, rgba(31, 41, 55, 0.8), rgba(31, 41, 55, 0.4));
    }

    html.dark .support-button {
        background: linear-gradient(135deg, var(--primary-color) 0%, #4338ca 100%);
    }

    /* Responsive Design Improvements */
    @media (max-width: 768px) {
        .support-hero {
            padding: 3rem 1.5rem;
        }
        
        .support-hero-title {
            font-size: 2.5rem;
        }
        
        .support-hero-subtitle {
            font-size: 1.1rem;
        }
        
        .support-hero-button {
            padding: 1rem 2rem;
        }
        
        .support-card-content {
            padding: 1.5rem;
        }
        
        .section-header {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }
        
        .section-icon-wrapper {
            margin: 0 auto 1rem;
        }
        
        .section-header::after {
            width: 50%;
            right: 25%;
        }
        
        .contact-item {
            flex-direction: column;
            text-align: center;
            padding: 2rem 1rem;
        }
        
        .contact-item::before {
            width: 100%;
            height: 4px;
            top: 0;
            left: 0;
        }
        
        .contact-icon-wrapper {
            margin: 0 auto 1rem;
        }
        
        .quote-box {
            padding: 1.5rem;
        }
        
        .quote-text {
            font-size: 1.1rem;
        }
        
        .announcement-text {
            font-size: 1.2rem;
        }
    }

    @media (max-width: 480px) {
        .support-hero-title {
            font-size: 2rem;
        }
        
        .support-hero-button {
            width: 100%;
            justify-content: center;
        }
    }
</style>

<div class="support-container">
    <!-- Hero Section with Particles -->
    <div class="support-hero">
        <div class="particles-container">
            <!-- Particles are created with JS -->
        </div>
        <div class="support-hero-content">
            <h1 class="support-hero-title">پشتیبانی لوتوس</h1>
            <p class="support-hero-subtitle">همراه شما در مسیر موفقیت</p>
            <div class="support-hero-cta">
                <a href="#contact" class="support-hero-button">
                    <i class="fas fa-headset"></i>
                    تماس با ما
                </a>
            </div>
        </div>
    </div>

    <div class="support-grid">
        <!-- About Us Card -->
        <div class="support-card">
            <div class="support-card-content">
                <div class="section-header">
                    <div class="section-icon-wrapper">
                        <i class="fas fa-info-circle section-icon"></i>
                    </div>
                    <h2 class="section-title">درباره ما</h2>
                </div>
                
                <div class="about-content">
                    <p>
                        ما در شناسنامه «گروه توسعه فناوری نیلوفرآبی» هستیم، ولی شما می‌توانید <span class="highlight-text">«لوتوس»</span> هم صدایمان کنید.
                    </p>
                    
                    <p>
                        داستان <span class="highlight-text">لوتوس</span> از آذرماه 1397 شروع شد. از تیم کوچکی که جانش برای یاد گرفتن فناوری و توسعه دادن هوشمندسازی در می‌ رفت.
                    </p>
                    
                    <div class="quote-box">
                        <p class="quote-text">
                            لوتوس به کاربران خود کمک می‌کند تا از ارتباطات تلفنی تجربه‌ای فراموش‌نشدنی را لمس کنند
                        </p>
                    </div>
                    
                    <p>
                        حالا می‌توانیم ادعا کنیم که دستیار هوشمند ارتباطات کاربرانی هستیم که به دنبال اطلاعات دقیق و به‌روز از مشتریان و مخاطبین خود هستند.
                    </p>
                    
                    <div class="announcement-box">
                        <p class="announcement-text">
                            به زودی ما را به نام «گروه نرم‌افزاری دانش‌بنیان نیلوفرآبی» می‌شناسید
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contact Card -->
        <div class="support-card" id="contact">
            <div class="company-image">
                <!-- Add tech pattern overlay -->
                <div class="tech-pattern"></div>
                <div class="lotus-logo">
                    <!-- Use the static logo file -->
                    <img src="{% static 'pic/logo.png' %}" alt="Lotus Logo" width="120" height="auto">
                </div>
            </div>
            
            <div class="support-card-content">
                <div class="section-header">
                    <div class="section-icon-wrapper">
                        <i class="fas fa-headset section-icon"></i>
                    </div>
                    <h2 class="section-title">تماس با ما</h2>
                </div>
                
                <div class="contact-methods">
                    <div class="contact-item">
                        <div class="contact-icon-wrapper">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h4>شماره تماس</h4>
                            <a href="tel:09030435699">09030435699</a>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon-wrapper">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h4>ایمیل</h4>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon-wrapper">
                            <i class="fab fa-telegram"></i>
                        </div>
                        <div class="contact-details">
                            <h4>کانال تلگرامی</h4>
                            <a href="#">مشاهده کانال</a>
                        </div>
                    </div>
                </div>
                
                <form class="support-form">
                    <div class="form-group">
                        <label for="name" class="form-label">نام و نام خانوادگی</label>
                        <div class="form-input-wrapper">
                            <input type="text" id="name" class="form-input" placeholder="نام خود را وارد کنید">
                            <i class="fas fa-user form-icon"></i>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="message" class="form-label">پیام شما</label>
                        <div class="form-input-wrapper">
                            <textarea id="message" class="form-textarea" placeholder="پیام خود را بنویسید"></textarea>
                            <i class="fas fa-comment form-icon"></i>
                        </div>
                    </div>
                    
                    <button type="submit" class="support-button">
                        <i class="fas fa-paper-plane"></i>
                        ارسال پیام
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Create particles for hero section
    document.addEventListener('DOMContentLoaded', function() {
        const particlesContainer = document.querySelector('.particles-container');
        const numberOfParticles = 50;
        
        for (let i = 0; i < numberOfParticles; i++) {
            const particle = document.createElement('div');
            particle.classList.add('particle');
            
            // Random position
            const posX = Math.random() * 100;
            const posY = Math.random() * 100;
            
            // Random size
            const size = Math.random() * 8 + 2;
            
            // Random opacity
            const opacity = Math.random() * 0.5 + 0.3;
            
            // Random animation delay
            const delay = Math.random() * 20;
            
            particle.style.left = `${posX}%`;
            particle.style.top = `${posY}%`;
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.opacity = opacity;
            particle.style.animationDelay = `${delay}s`;
            
            particlesContainer.appendChild(particle);
        }
    });
</script>

{% endblock %}