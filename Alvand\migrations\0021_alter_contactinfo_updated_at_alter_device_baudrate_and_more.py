# Generated by Django 5.1.6 on 2025-03-07 09:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Alvand', '0020_alter_telephons_created_at'),
    ]

    operations = [
        migrations.AlterField(
            model_name='contactinfo',
            name='updated_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='device',
            name='baudrate',
            field=models.BigIntegerField(blank=True, choices=[(4800, '4800'), (9600, '9600'), (19200, '19200'), (38400, '38400'), (57600, '57600'), (112500, '112500'), (230400, '230400'), (460800, '460800'), (921600, '921600')], null=True),
        ),
        migrations.AlterField(
            model_name='device',
            name='databits',
            field=models.IntegerField(blank=True, choices=[(5, '5'), (6, '6'), (7, '7'), (8, '8')], null=True),
        ),
        migrations.AlterField(
            model_name='device',
            name='stopbits',
            field=models.FloatField(blank=True, choices=[(1, '1'), (1.5, '1.5'), (2, '2')], null=True),
        ),
    ]
