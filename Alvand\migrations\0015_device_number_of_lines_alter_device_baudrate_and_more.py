# Generated by Django 5.1.6 on 2025-03-04 07:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Alvand', '0014_device_device'),
    ]

    operations = [
        migrations.AddField(
            model_name='device',
            name='number_of_lines',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='device',
            name='baudrate',
            field=models.BigIntegerField(blank=True, choices=[('4800', '4800'), ('9600', '9600'), ('19200', '19200'), ('38400', '38400'), ('57600', '57600'), ('112500', '112500'), ('230400', '230400'), ('460800', '460800'), ('921600', '921600')], null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='device',
            name='databits',
            field=models.IntegerField(blank=True, choices=[('5', '5'), ('6', '6'), ('7', '7'), ('8', '8')], null=True),
        ),
        migrations.AlterField(
            model_name='device',
            name='device',
            field=models.CharField(choices=[('KX-TA 308', 'KX-TA 308'), ('KX-TES 824', 'KX-TES 824'), ('KX-TEM 824', 'KX-TEM 824')], max_length=191),
        ),
        migrations.AlterField(
            model_name='device',
            name='flow',
            field=models.TextField(blank=True, choices=[('None', 'None'), ('XON/XOFF', 'XON/XOFF'), ('RTS/CTS', 'RTS/CTS'), ('DSR/DTR', 'DSR/DTR')], null=True),
        ),
        migrations.AlterField(
            model_name='device',
            name='parity',
            field=models.TextField(blank=True, choices=[('None', 'None'), ('Odd', 'Odd'), ('Even', 'Even'), ('Mark', 'Mark'), ('Space', 'Space')], null=True),
        ),
        migrations.AlterField(
            model_name='device',
            name='stopbits',
            field=models.FloatField(blank=True, choices=[('1', '1'), ('1.5', '1.5'), ('2', '2')], null=True),
        ),
        migrations.CreateModel(
            name='ContactInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('province', models.CharField(choices=[('0', 'آذربایجان شرقی'), ('1', 'آذربایجان غربی'), ('2', '\tاردبیل'), ('3', 'اصفهان'), ('4', 'البرز'), ('5', 'ایلام'), ('6', 'بوشهر'), ('7', '\tتهران'), ('8', 'چهارمحال و بختیاری'), ('9', 'خراسان جنوبی'), ('10', 'خراسان رضوی'), ('11', 'خراسان شمالی'), ('12', 'خوزستان'), ('13', 'زنجان'), ('14', 'سمنان'), ('15', 'سیستان و بلوچستان'), ('16', 'فارس'), ('17', 'قزوین'), ('18', 'قم'), ('19', 'کردستان'), ('20', 'کرمان'), ('21', 'کرمانشاه'), ('22', 'کهگیلویه و بویراحمد'), ('23', 'گلستان'), ('24', 'گیلان'), ('25', 'لرستان'), ('26', 'مازندران'), ('27', 'مرکزی'), ('28', 'هرمزگان'), ('29', 'همدان'), ('30', 'یزد')], max_length=191, verbose_name='استان:')),
                ('phone_number', models.CharField(max_length=191, verbose_name='شماره همراه مدیر:')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField()),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='Alvand.users')),
            ],
        ),
    ]
