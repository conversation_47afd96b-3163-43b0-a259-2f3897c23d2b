# Generated by Django 5.1.6 on 2025-04-04 21:24

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Alvand', '0028_delete_externaldbinfos'),
    ]

    operations = [
        migrations.AddField(
            model_name='lices',
            name='version',
            field=models.CharField(choices=[('0', 'الوند'), ('1', 'بینالود')], default=django.utils.timezone.now, max_length=50),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='device',
            name='device',
            field=models.CharField(choices=[('KX-TA 308', 'KX-TA 308'), ('KX-TES 824', 'KX-TES 824'), ('KX-TEM 824', 'KX-TEM 824'), ('KX-TDA30', 'KX-TDA30'), ('KX-TDA100', 'KX-TDA100'), ('KX-TDA100D', 'KX-TDA100D'), ('KX-TDA100DBA', 'KX-TDA100DBA'), ('KX-TDA200', 'KX-TDA200'), ('KX-TDA600', 'KX-TDA600'), ('KX-TDE100', 'KX-TDE100'), ('KX-TDE200', 'KX-TDE200'), ('KX-TDE600', 'KX-TDE600'), ('KX-NS300', 'KX-NS300'), ('KX-NS500', 'KX-NS500'), ('KX-NS700', 'KX-NS700'), ('KX-NS1000', 'KX-NS1000'), ('KX-HTS32', 'KX-HTS32'), ('KX-HTS824', 'KX-HTS824')], max_length=191),
        ),
    ]
