import datetime
from django.core.paginator import <PERSON><PERSON><PERSON>, PageNotAnInteger, EmptyPage, InvalidPage
from django.http import HttpResponseRedirect
from django.shortcuts import render, redirect
from django.urls import reverse_lazy
from django.views.generic import TemplateView, View, FormView
from django.contrib import messages
from django.contrib.auth.hashers import check_password, make_password
from django.core.mail import send_mail
from .forms import *
from .models import *
from functools import wraps
import math, jdatetime, wmi, pythoncom, random, os, sys
from django.db.models import Q
from django.utils import timezone

upload = os.path.join("Alvand/static/upload")
os.makedirs(upload, exist_ok=True)

errors = [
(211, 'Speech path loop-back check error', '• Optional service card malfunction: DHLC, DLC, SLC (SLC8, ESLC16, EMSLC16), CSIF, T1, E1, BRI, PRI, OPB3, E&M, IP-GW, DID, ELCOT', '• See if the corresponding optional service card is installed properly.\n• Pull out and re-insert the corresponding optional service card.\n• Press the Reset Button\n• Replace the corresponding optional service card'),
(212, 'Echo canceller access error', '• Optional service card malfunction: CSIF, EECHO', '• See if the corresponding optional service card is installed properly.\n• Pull out and re-insert the corresponding optional service card.\n• Press the Reset Button\n• Replace the corresponding optional service card'),
(214, 'DSP Boot check error', '• Optional service card malfunction: T1, E1', '• See if the corresponding optional service card is installed properly.\n• Pull out and re-insert the corresponding optional service card.\n• Press the Reset Button\n• Replace the corresponding optional service card'),
(215, 'Framer IC access error', '• Optional service card malfunction: T1, E1, BRI, PRI', '• See if the corresponding optional service card is installed properly.\n• Pull out and re-insert the corresponding optional service card.\n• Press the Reset Button\n• Replace the corresponding optional service card'),
(216, 'MSG card DSP error', '• Optional service card malfunction: MSG, OPB3', '• See if the corresponding optional service card is installed properly.\n• Pull out and re-insert the corresponding optional service card.\n• Press the Reset Button\n• Replace the corresponding optional service card'),
(217, 'MSG card data error', '• Optional service card malfunction: MSG, OPB3\n• Erroneous recording of messages', '• See if the corresponding optional service card is installed properly\n• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Re-record the messages\n• Replace the corresponding optional service card'),
(218, 'LANC register access error', '• Detection of accessing error to LAN controller of CTI-LINK card', '• See if the corresponding optional service card is installed properly\n• Pull out and re-insert the corresponding optional service card\n• Replace the corresponding optional service card'),
(220, 'Flash ROM access error', '• Detection of accessing error to Flash ROM of optional service card', '• See if the corresponding optional service card is installed properly\n• Pull out and re-insert the corresponding optional service card\n• Replace the corresponding optional service card'),
(221, 'BUS Controller access error', '• Optional service card malfunction: BUS-S', '• See if the corresponding optional service card is installed properly\n• Pull out and re-insert the corresponding optional service card\n• Replace the corresponding optional service card'),
(223, 'FPGA access error', 'Optional service card malfunction: FPGA', 'Replace the corresponding optional service card'),
(0, 'MPR WDT overflow', '• EMPR card malfunction\n• Optional service card malfunction: CTI-LINK, BUS-S\n• Erroneous processing of EMPR card software\n• Software error due to external factors', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(1, 'SDRAM bit error', '• EMPR card malfunction\n• Optional service card malfunction: CTI-LINK, BUS-S\n• Erroneous processing of EMPR card software\n• Software error due to external factors', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(2, 'System Restart', '• Reset Button is pressed\n• Power failure\n• EMPR card malfunction\n• Erroneous processing of EMPR card software\n• Software error due to external factors', '• Ignore if not frequent \n• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the EMPR card(be sure to turn off the Hybrid IP-PBX when replacing)'),
(3, 'System start up error', '• PBX main board malfunction\n• Erroneous processing of PBX software\n• Software error due to external factors', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Exchange the PBX'),
(4, 'Common process error (Minor)', '• PBX main board malfunction\n• Erroneous processing of PBX software\n• Software error due to external factors', '• This message indicates an automatic recovery from a minor software fault'),
(5, 'Common process error (Major)', '• PBX main board malfunction\n• Erroneous processing of PBX software\n• Software error due to external factors', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Exchange the PBX'),
(6, 'Common process error (Critical)', '• PBX main board malfunction\n• Erroneous processing of PBX software\n• Software error due to external factors', '• This message indicates an automatic recovery via system reset from a major software fault\n• Exchange the PBX'),
(10, 'AC power down', '• Power supply system malfunction (e.g., power failure, power noise, trouble with UPS)\n• Bad connection or breaking of AC cord\n• Power supply circuit (PSU, back board) malfunction', '• Check the power supply system\n• See if the AC cord is connected properly\n• Check the AC cord\n• Replace the AC cord (be sure to turn off the Hybrid IP-PBX when replacing)\n• Replace the PSU (be sure to turn off the Hybrid IP-PBX when replacing)\n• Replace the back board (be sure to turn off the Hybrid IP-PBX when replacing）'),
(11, 'DC power down', '• AC power down\n• Power supply circuit (PSU, back board) malfunction\n• Detection of over current (short circuit on optional service cards)', '• Check the power supply system\n• See if the AC cord is connected properly\n• Check the AC cord\n• Replace the AC cord (be sure to turn off the Hybrid IP-PBX when replacing)\n• Replace the PSU (be sure to turn off the Hybrid IP-PBX when replacing)\n• Replace the back board (be sure to turn off the Hybrid IP-PBX when replacing)\n• Remove the optional service cards and restart the Hybrid IP-PBX'),
(12, 'RAM battery low', '• Battery out', '• Replace the EMPR card'),
(14, 'FAN Alarm', '• EMPR card malfunction\n• BUS-S card malfunction', '• See if anything is jammed in the fan\n• Exchange the PBX'),
(16, 'CS overload', '• PSU-L malfunction', '• See if anything is jammed in the fan\n• Replace the PSU (be sure to turn off the Hybrid IP-PBX when replacing)'),
(17, 'BRI port overload', '• Defective cable\n• CS malfunction\n• Optional service card malfunction: CSIF', '• Check the cable diameter and length\n• Replace the CS\n• Replace the corresponding optional service card'),
(20, 'SD file access error', '• Defective cable\n• Defective ISDN terminal equipment\n• Optional service card malfunction: BRI', '• Check the cable\n• Replace the defective terminal equipment\n• Check the number of connected terminal equipment\n• Replace the corresponding optional service card'),
(21, 'SD Memory Card disconnected', '• SD Memory Card malfunction\n• Bad connection of SD Memory Card\n• EMPR card malfunction', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the SD Memory Card\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(22, 'Not enough free space on SD card', '• Not enough memory space available to save the system data, or to upload system files from the KX-TDA600 Maintenance Console', '• Delete the files whose file names start with \"$\" from SD Memory Card\n• Delete the \"Pxxx\" files (old programme files of optional service cards) from SD Memory Card. \"xxx\" indicates the card type (e.g., \"PDHLC\" for DHLC card)\nNote: Do not delete the \"PLMPR\" file; it is the programme file of the EMPR card.'),
(23, 'System data file version error', '• Old system files on SD Memory Card\n• Defective system files on SD Memory Card', '• Restore the backup files\n• Re-install the software'),
(24, 'System initialization file version error', '• Old system files on SD Memory Card\n• Defective system files on SD Memory Card', '• Restore the backup files\n• Re-install the software'),
(25, 'Card initialization file version error', '• Old system files on SD Memory Card\n• Defective system files on SD Memory Card', '• Restore the backup files\n• Re-install the software'),
(26, 'LCD file version error', '• Old system files on SD Memory Card\n• Defective system files on SD Memory Card', '• Restore the backup files\n• Re-install the software'),
(27, 'System data file checksum error', '• Old system files on SD Memory Card\n• Defective system files on SD Memory Card', '• Restore the backup files\n• Re-install the software'),
(28, 'System initialization file checksum error', '• Old system files on SD Memory Card\n• Defective system files on SD Memory Card', '• Restore the backup files\n• Re-install the software'),
(29, 'Card initialization file checksum error', '• Old system files on SD Memory Card\n• Defective system files on SD Memory Card', '• Restore the backup files\n• Re-install the software'),
(30, 'LCD file checksum error', '• Old system files on SD Memory Card\n• Defective system files on SD Memory Card', '• Restore the backup files\n• Re-install the software'),
(31, 'System data file not found', '• SD Memory Card not installed\n• Bad connection of SD Memory Card\n• SD Memory Card malfunction\n• EMPR card malfunction', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the SD Memory Card\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(32, 'System initialization file not found', '• SD Memory Card not installed\n• Bad connection of SD Memory Card\n• SD Memory Card malfunction\n• EMPR card malfunction', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the SD Memory Card\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(33, 'Card initialization file not found', '• SD Memory Card not installed\n• Bad connection of SD Memory Card\n• SD Memory Card malfunction\n• EMPR card malfunction', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the SD Memory Card\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(34, 'LCD file not found', '• SD Memory Card not installed\n• Bad connection of SD Memory Card\n• SD Memory Card malfunction\n• EMPR card malfunction', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the SD Memory Card\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(35, 'System data file access error', '• SD Memory Card not installed\n• Bad connection of SD Memory Card\n• SD Memory Card malfunction\n• EMPR card malfunction', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the SD Memory Card\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(36, 'System initialization file access error', '• SD Memory Card not installed\n• Bad connection of SD Memory Card\n• SD Memory Card malfunction\n• EMPR card malfunction', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the SD Memory Card\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(37, 'Card initialization file access error', '• SD Memory Card not installed\n• Bad connection of SD Memory Card\n• SD Memory Card malfunction\n• EMPR card malfunction', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the SD Memory Card\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(38, 'LCD file access error', '• SD Memory Card not installed\n• Bad connection of SD Memory Card\n• SD Memory Card malfunction\n• EMPR card malfunction', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the SD Memory Card\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(39, 'SD file access error', '• SD Memory Card not installed\n• Bad connection of SD Memory Card\n• SD Memory Card malfunction\n• EMPR card malfunction', '• Press the Reset Button\n• Reprogramme the Hybrid IP-PBX\n• Replace the SD Memory Card\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(41, 'Expansion Memory Card Failure', '• EMEC card is not properly connected', '• See if the corresponding optional service card is installed properly\n• Pull out and re-insert the corresponding optional service card\n• Replace the corresponding optional service card'),
(42, 'Shelf FAN Alarm', '• Fan malfunction (Basic Shelf or Expansion Shelf)', '• Turn off the Hybrid IP-PBX\n• See if anything is jammed in the fan\n• Replace the fan of the corresponding shelf (be sure to turn off the Hybrid IP-PBX when replacing)'),
(43, 'Expansion Shelf communication error', '• Bus Cable is not properly connected between shelves\n• Optional service card malfunction: BUS-M, BUS-ME, BUS-S', '• See if the Bus Cable is connected properly\n• Replace the corresponding optional service card (be sure to turn off the Hybrid IP-PBX when replacing a BUS-M card)'),
(44, 'Master CS ID duplicated', '• A CS ID number registered as the Master CS ID is also detected on another shelf', '• Unplug the CS of the corresponding port, and plug it back into the correct port, then plug a new CS in the corresponding port.'),
(45, 'Expansion Shelf communication recovery', 'A communication error between shelves was recovered from', 'This message indicates when a communication error between shelves was recovered from'),
(46, 'CF life is running out', 'The usage lifetime limit of the CF Card is approaching', 'Replace the CF Card (Turn off the power switch of the PBX before doing so)'),
(47, 'Program update failure', 'A Program Update failed', 'Confirm that the Program Update file is valid'),
(48, 'Program Recovery', 'Due to an error in the operation of the Program Update, the previous programme version was restored', 'Confirm that the Program Update file is valid'),
(49, 'Expansion VOIP DSP link error', 'A communication error is occurring between the PBXُs mother board and a DSP card', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Confirm that the DSP card is properly installed (Turn off the power switch of the PBX before doing so)\n• Replace the DSP card (Turn off the power switch of the PBX before doing so)\n• Exchange the PBX'),
(50, 'L2SW error', 'PBX main board malfunction', 'Exchange the PBX'),
(51, 'Network MSW was not transmitted. (Counter for retransmission was exceeded.)', 'Network Message Waiting notifications were unsuccessfully re-transmitted the maximum number of times', '• See if the corresponding optional service card is installed properly\n• Replace the corresponding optional service card\n• Check the network connections\n• Confirm that none of the PBXs in the network are in OUS or FAULT status, or turned off'),
(52, 'Network MSW was not transmitted. (Buffer for retransmission was exceeded.)', 'The buffer of Network Message Waiting notifications to be retransmitted is full, so new notifications can not be added', '• Fix the source of Network Message Waiting transmission errors, to reduce the number of re-transmissions\n• Contact your dealer for information on finding the source of transmission errors'),
(53, 'UPS disconnected', 'The connection between the PBX and the UPS was broken', '• Check the USB cable connecting the PBX to the UPS\n• Confirm that the UPS is functioning normally'),
(54, 'UPS connected', 'The PBX was connected to a UPS', 'This message indicates when the PBX was connected to a UPS'),
(55, 'UPS power supply start', 'Due to a loss of power or irregularity with the utility power source, the PBX began drawing power from the UPS', '• Check the status of the utility power source\n• Check the connection of the UPS to the utility power source'),
(56, 'UPS power supply end', 'The utility power source was restored to normal operation, and the PBX stopped drawing power from the UPS', 'This message indicates when the PBX stops drawing power from the UPS'),
(57, 'UPS battery run out', 'The UPS batterywas completely drained while it was supplying power to the PBX', '• Recharge the UPS battery\n• Replace the UPS'),
(58, 'USB overcurrent', 'A device that causes an overcurrent to occur at the USB port has been connected', 'Remove the device connected to the USB port'),
(59, 'USB restore succeeded', 'A data restore operation from a USB memory device was successful', 'This message indicates that a data restore operation was successful'),
(60, 'USB restore failed', 'A data restore operation from a USB memory device failed', 'Check that the backup data stored on the USB memory device is valid'),
(61, 'Shelf cabinet mismatch', '• Cabinet information mismatch between Legacy GW shelf installed on Web Maintenance Console and Legacy GW shelf actually connected.\n• Connected Legacy GW shelf is not supported.', '• Reprogram the PBX\n• Confirm that connected Legacy GW is supported.'),
(62, 'System Data converted successfully', 'System Data converted successfully by program update.', 'This message indicates System Data was converted successfully.'),
(63, 'Failed to convert System Data', 'System Data convert failure during program update', 'Convert System data with Off-lineWEB-Maintenance Console, transfer it to the PBX and reboot'),
(64, 'Failed to conver System Data (Out of memory)', 'Failed to convert System Data due to lack of free space in CF Card or USB memory during program update.', '• Please execute program update again after inserting USB memory which has enough free space.\n• Please execute program update again after ensuring sufficient free space on USB memory by deleting unnecessary files.'),
(65, 'SD card access error', '• SD card malfunction\n• Bad connection of SD card\n• PBX main board malfunction', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Reprogram the PBX\n• Confirm that the SD card is properly installed\n• Replace the SD card\n• Exchange the PBX'),
(66, 'SD card disconnected', '• SD card not installed\n• Bad connection of SD card\n• SD card malfunction\n• PBX main board malfunction', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Reprogram the PBX\n• Confirm that the SD card is properly installed\n• Replace the SD card\n• Exchange the PBX'),
(67, 'Not enough free space on SD card', 'Not enough memory space available to save the data', 'Remove unnecessary files from the SD card'),
(68, 'MPR Flash ROM access error', '• MPR Flash ROM malfunction\n• Bad connection of MPR Flash ROM\n• PBX main board malfunction', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Reprogram the PBX\n• Exchange the PBX'),
(69, 'Not enough free space on MPR Flash ROM', 'Not enough memory space available to save the data', 'Remove unnecessary files from the MPR Flash ROM'),
(70, 'Shelf cabinet limitation over', 'Shelf cabinet limitation over', 'Renew Activation Key (Expansion Capacity)'),
(71, 'TCP Trace is stopped', '• USB memory disconnected during TCP Trace.\n• TCP Trace is stopped due to lack of free space in USB memory.', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Confirm that USB memory is properly inserted.\n• Please execute TCP Trace again after inserting USB memory which has enough free space.\n• Please execute TCP Trace again after ensuring sufficient free space on USB memory by deleting unnecessary files.'),
(90, 'Over Card Limitation', '• Too many optional service cards installed', '• Reduce the number of optional service cards'),
(91, 'PT connection over', '• Too many PTs connected', '• Reduce the number of PTs'),
(92, 'CS connection over', '• Too many CSs connected', '• Reduce the number of CSs'),
(119, 'Duplicate IP segment', 'WAN, LAN and MNT segments are overlapped', 'Consult your network administrator'),
(120, 'MPR VoIP-DSP failure', 'A malfunction occurred in the DSP on the PBX mother board', '• Ignore if not frequent\n• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Exchange the PBX'),
(121, 'MPR VoIP-DSP boot failure', 'A malfunction occurred in the DSP on the PBX mother board', '• Ignore if not frequent\n• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Exchange the PBX'),
(122, 'Virtual card start up error', 'PBX main board malfunction', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Reprogramme the PBX\n• Exchange the PBX'),
(123, 'Duplicate IP address', 'IP address is identical to another device\'s on the Network', 'Consult your network administrator'),
(124, 'LAN interface start up error', 'PBX main board LAN device malfunction', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Exchange the PBX'),
(125, 'Login authentication failure', 'Login authentication failure three times in a row', 'Login using a correct Login ID and Password after waiting more than 10 minutes'),
(127, 'Activation Key was expired (SHGW4)', 'Activation Key has expired', 'Renew Activation Key'),
(128, 'Activation Key was expired (IPPT8)', 'Activation Key has expired', 'Renew Activation Key'),
(129, 'Activation Key was expired (SPE16)', 'Activation Key has expired', 'Renew Activation Key'),
(130, 'Activation Key was expired (IPPTS8)', 'Activation Key has expired', 'Renew Activation Key'),
(131, 'Activation Key was expired (UCAV2)', 'Activation Key has expired', 'Renew Activation Key'),
(132, 'Collection of date and time was failed (SNTP client)', '• SNTP server is not active\n• Network malfunction', 'Consult your network administrator'),
(133, 'Acquisition of network information was failed (DHCP client)', '• DHCP server is not active\n• Network malfunction', 'Consult your network administrator'),
(134, 'Extension of lease was failed (DHCP client)', '• DHCP server is not active\n• Network malfunction', 'Consult your network administrator'),
(135, 'Lease time was expired (DHCP client)', '• DHCP server is not active\n• Network malfunction', 'Consult your network administrator'),
(136, 'IP extension or trunk limitation over', 'IP extension or trunk limitation over', 'Reduce the number of IP extension or trunk'),
(137, 'Activation Key was expired (others)', 'Activation Key has expired', 'Renew Activation Key'),
(138, 'Two-way Recording or Mobile user limitation over', 'The limit on the number of two-way recording or mobile extension users was exceeded', 'Reduce the number of two-way recording or mobile extension users'),
(139, 'UM channel limitation over', 'The limit of the number of assignable UM ports was exceeded', 'Reduce the number of UM ports in use'),
(140, 'ActivationKey was expired (Pre-installed licence)', 'Activation Key has expired', 'Renew Activation Key'),
(141, 'ActivationKey was expired (IP extension)', 'Activation Key has expired', 'Renew Activation Key'),
(142, 'ActivationKey was expired (Network)', 'Activation Key has expired', 'Renew Activation Key'),
(143, 'ActivationKey was expired (UM)', 'Activation Key has expired', 'Renew Activation Key'),
(144, 'ActivationKey was expired (Main Unit Function)', 'Activation Key has expired', 'Renew Activation Key'),
(145, 'ActivationKey was expired (Service)', 'Activation Key has expired', 'Renew Activation Key'),
(146, 'ActivationKey was expired (IP-CS channel expansion)', 'Activation Key has expired', 'Renew Activation Key'),
(150, 'FTP server authentication failure', '• Authentication failed when logging in to the FTP server\n• FTP server is not active\n• Network malfunction', '• Confirm account information (ID, Password)\n• Consult your network administrator'),
(151, 'SMTP server authentication failure', '• Authentication failed when logging in to the SMTP server\n• SMTP server is not active\n• Network malfunction', '• Confirm account information (ID, Password)\n• Consult your network administrator'),
(153, 'FTP client limitation over', 'Too many simultaneous FTP client connections', 'This message indicates when the number of FTP clients logging in to the PBX exceeds limitations'),
(154, 'CA private key/certification error', 'The CA private key or the CA certificate data has been damaged', 'Exchange the PBX'),
(155, 'Server secret key/certification error', 'The server private key or the server certificate data has been damaged', 'This message indicates when the server private key or the server certificate data has been automatically restored'),
(156, 'E-Mail send failure', 'Network malfunction', '• Check the settings in Web Maintenance\n• Consult your network administrator'),
(171, 'ActivationKey was expired (Poltys C.Bridge)', 'Activation Key has expired', 'Renew Activation Key'),
(172, 'ActivationKey was expired (Poltys CA RCS-Start)', 'Activation Key has expired', 'Renew Activation Key'),
(173, 'ActivationKey was expired (Poltys CA RCS-Extend)', 'Activation Key has expired', 'Renew Activation Key'),
(174, 'ActivationKey was expired (PSDN Option-1)', 'Activation Key has expired', 'Renew Activation Key'),
(175, 'ActivationKey was expired (PSDN Option-2)', 'Activation Key has expired', 'Renew Activation Key'),
(176, 'ActivationKey was expired (Poltys CC Series)', 'Activation Key has expired', 'Renew Activation Key'),
(200, 'LPR start up error (ROM NG)', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT', '• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Replace the corresponding optional service card'),
(201, 'LPR start up error (RAM NG)', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT', '• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Replace the corresponding optional service card'),
(202, 'LPR start up error (No Program)', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT', '• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Update the software of the corresponding optional service card\n• Replace the corresponding optional service card'),
(203, 'LPR start up error (Version NG)', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT', '• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Update the software of the corresponding optional service card\n• Replace the corresponding optional service card'),
(204, 'LPR start up error (Download NG)', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT', '• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Update the software of the corresponding optional service card\n• Replace the corresponding optional service card'),
(205, 'LPR start up error (No response)', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT', '• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Update the software of the corresponding optional service card\n• Replace the corresponding optional service card'),
(206, 'LPR start up error (Card type NG)', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT', '• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Update the software of the corresponding optional service card\n• Replace the corresponding optional service card'),
(207, 'LPR start up error (Check SUM NG)', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT', '• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Update the software of the corresponding optional service card\n• Replace the corresponding optional service card'),
(208, 'Instantaneous Interruption error', 'A combination card malfunction due to an instantaneous interruption has been automatically recovered from', 'This message indicates when a combination card malfunction that occurred due to an instantaneous interruption has been automatically recovered'),
(224, 'Expansion VoIP-DSP failure', 'Optional service card malfunction: VoIP-DSP', '• Ignore if not frequent\n• Confirm that the DSP card is properly installed (Turn off the power switch of the PBX before doing so)\n• Replace the DSP card (Turn off the power switch of the PBX before doing so)'),
(225, 'Expansion VoIP-DSP boot failure', 'Optional service card malfunction: VoIP-DSP', '• Ignore if not frequent\n• Confirm that the DSP card is properly installed (Turn off the power switch of the PBX before doing so)\n• Replace the DSP card (Turn off the power switch of the PBX before doing so)'),
(226, 'MOH failure', 'PBX main board malfunction', 'Exchange the PBX'),
(227, 'MOH data restore', 'MOH data that was not successfully installed due to power loss, etc. has been automatically restored', 'This message indicates when MOH data that was not installed successfully was re-installed'),
(230, 'Card disconnected', '• Optional service card not installed properly\n• Optional service card malfunction\n• Back board malfunction', '• See if the corresponding optional service card is installed properly\n• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Replace the corresponding optional service card\n• Replace the back board (be sure to turn off the Hybrid IP-PBX when replacing)'),
(231, 'LPR alive check error', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT\n• Back board malfunction\n• EMPR card malfunction', '• See if the corresponding optional service card is installed properly\n• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Replace the corresponding optional service card\n• Replace the back board (be sure to turn off the Hybrid IP-PBX when replacing)\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(232, 'MPR-LPR communication error', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT\n• Back board malfunction\n• EMPR card malfunction', '• See if the corresponding optional service card is installed properly\n• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Replace the corresponding optional service card\n• Replace the back board (be sure to turn off the Hybrid IP-PBX when replacing)\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(233, 'LPR data check error', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT\n• Back board malfunction\n• EMPR card malfunction', '• See if the corresponding optional service card is installed properly\n• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Replace the corresponding optional service card\n• Replace the back board (be sure to turn off the Hybrid IP-PBX when replacing)\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(234, 'DPLL clock failure', '• Optional service card malfunction: DHLC, DLC, CSIF, T1, E1, BRI, PRI, OPB3, CTI-LINK, E&M, IP-GW, DID, SLC (SLC8, ESLC16, EMSLC16), IP-EXT, ELCOT\n• Back board malfunction\n• EMPR card malfunction', '• See if the corresponding optional service card is installed properly\n• Pull out and re-insert the corresponding optional service card\n• Press the Reset Button\n• Replace the corresponding optional service card\n• Replace the back board (be sure to turn off the Hybrid IP-PBX when replacing)\n• Replace the EMPR card (be sure to turn off the Hybrid IP-PBX when replacing)'),
(235, 'CS clock failure', '• Optional service card malfunction: CSIF', '• Replace the corresponding optional service card'),
(237, 'Illegal CS connection', 'Incorrect CS connection', 'Connect CS correctly'),
(250, 'T1/E1 DSP failure', '• Optional service card malfunction: T1, E1', '• See if the corresponding optional service card is installed properly\n• Replace the corresponding optional service card'),
(251, 'MSG DSP failure', '• Optional service card malfunction: MSG', '• See if the corresponding optional service card is installed properly\n• Replace the corresponding optional service card'),
(252, 'FPGA failure', 'Optional service card malfunction: FPGA', 'Replace the corresponding optional service card'),
(260, 'UM information recorded(Minor)', '• PBX main board malfunction\n• Erroneous processing of PBX software\n• Software error due to external factors', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Reprogramme the PBX\n• Exchange the PBX'),
(261, 'UM information recorded(Major)', '• PBX main board malfunction\n• Erroneous processing of PBX software\n• Software error due to external factors', '• Perform a system reset of the PBX through Web Maintenance Console (If the system reset cannot be performed, turn off the power switch, and then turn it on)\n• Reprogramme the PBX\n• Exchange the PBX'),
(262, 'All UM ports exceeded the busy threshold time [hh:mm:ss]', '• All UM ports exceeded the busy threshold time\n• [hh:mm:ss] shows busy continuation time', 'Recommend an increase of Activation Key for UM'),
(300, 'Digital trunk out of Synchronization', '• Network (digital trunk) malfunction\n• Optional service card malfunction: PRI, T1, E1\n• Wrong A/B switch setting: PRI, T1, E1\n• Wrong termination switch setting: PRI30, E1', '• Check the signals from the network\n• Check the cable\n• See if the A/B switch is set to A on the corresponding optional service card\n• See if the termination switch is set properly on the corresponding optional service card: 120 Ω when using RJ45 connector; 75  Ω when using BNC connector\n• See if the corresponding optional service card is installed properly\n• Replace the corresponding optional service card'),
(301, 'Digital trunk RAI Reception', '• Network (digital trunk) malfunction\n• Optional service card malfunction: PRI, T1, E1\n• Wrong A/B switch setting: PRI, T1, E1\n• Wrong termination switch setting: PRI30, E2', '• Check the signals from the network\n• Check the cable\n• See if the A/B switch is set to A on the corresponding optional service card\n• See if the termination switch is set properly on the corresponding optional service card: 120 Ω when using RJ45 connector; 75  Ω when using BNC connector\n• See if the corresponding optional service card is installed properly\n• Replace the corresponding optional service card'),
(302, 'Digital trunk AIS Reception', '• Network (digital trunk) malfunction\n• Optional service card malfunction: PRI, T1, E1\n• Wrong A/B switch setting: PRI, T1, E1\n• Wrong termination switch setting: PRI30, E3', '• Check the signals from the network\n• Check the cable\n• See if the A/B switch is set to A on the corresponding optional service card\n• See if the termination switch is set properly on the corresponding optional service card: 120 Ω when using RJ45 connector; 75  Ω when using BNC connector\n• See if the corresponding optional service card is installed properly\n• Replace the corresponding optional service card'),
(303, 'Multiframe out of Synchronization', '• Network (digital trunk) malfunction\n• Optional service card malfunction: PRI, T1, E1\n• Wrong A/B switch setting: PRI, T1, E1\n• Wrong termination switch setting: PRI30, E4', '• Check the signals from the network\n• Check the cable\n• See if the A/B switch is set to A on the corresponding optional service card\n• See if the termination switch is set properly on the corresponding optional service card: 120 Ω when using RJ45 connector; 75  Ω when using BNC connector\n• See if the corresponding optional service card is installed properly\n• Replace the corresponding optional service card'),
(304, 'Frame error', '• Network (digital trunk) malfunction\n• Optional service card malfunction: PRI, T1, E1\n• Wrong A/B switch setting: PRI, T1, E1\n• Wrong termination switch setting: PRI30, E5', '• Check the signals from the network\n• Check the cable\n• See if the A/B switch is set to A on the corresponding optional service card\n• See if the termination switch is set properly on the corresponding optional service card: 120 Ω when using RJ45 connector; 75  Ω when using BNC connector\n• See if the corresponding optional service card is installed properly\n• Replace the corresponding optional service card'),
(305, 'Data Link failure', '• Data link between the CS and CSIF card or DHLC/DLC card failed\n• Data link between the network and PRI/BRI card failed\n• Data link between the network and IP-GW card failed', '• Check the connection between the CS and CSIF card or DHLC/DLC card\n• Check the connection between the network and PRI/BRI card\n• Check the connection between the network and IP-GW card'),
(306, 'E1 Channel Block failure', '• Network (digital trunk) malfunction\n• Optional service card malfunction: E1\n• Wrong A/B switch setting: E1\n• Wrong termination switch setting: E1', '• Check the signals from the network\n• Check the cable\n• See if the A/B switch is set to A on the corresponding optional service card\n• See if the termination switch is set properly on the corresponding optional service card: 120 Ω when using RJ45 connect'),
(307, 'LAN No Carrier', '• IP-GW card not connected to the LAN', '• Check the connection between the LAN and IP-GW card'),
(308, 'IP-GW LAN Loop back Error', '• Detection of IP-GW LAN Loop back Test error', '• Replace the corresponding optional service card\n• Collect the log data of IP-GW (refer to the documentation for the IP-GW card)'),
(309, 'IP-GW Core Data Link Error', '• Detection of IP-GW Core data Link error', '• Press the Reset Button\n• Collect the log data of IP-GW (refer to the documentation for the IP-GW card)'),
(310, 'Port Link Failure', '• Voice Processing System malfunction\n• Ports defective on optional service card: DHLC, DLC', '• Check the Voice Processing System\n• See if the corresponding optional service card is installed properly\n• Replace the corresponding optional service card'),
(320, 'IP-GW H.323 Dummy Call Test Error', '• Detection of IP-GW H.323 Dummy Call Test error', '• Replace the corresponding optional service card\n• Collect the log data of IP-GW (refer to the documentation for the IP-GW card)'),
(321, 'IP-GW Gatekeeper Error', '• Detection of Gatekeeper access error', '• Check the IP address setting of Gatekeeper\n• Check whether the Gatekeeper is connected to the network and work properly\n• Check the route to the Gatekeeper'),
(322, 'IP-GW Gatekeeper Registration Error', '• Gatekeeper Registration is failed', '• Check the Gatekeeper setting'),
(323, 'IP-GW SDRAM Failure', '• Detection of IP-GW SDRAM error', '• Replace the corresponding optional service card'),
(324, 'IP-GW DPRAM Failure', '• Detection of IP-GW DPRAM error', '• Replace the corresponding optional service card'),
(325, 'IP-GW LAN Chip Failure', '• Detection of IP-GW LAN Chip failure', '• Replace the corresponding optional service card\n• Collect the log data of IP-GW (refer to the documentation for the IP-GW card)'),
(326, 'IP-GW Stop', '• IP-GW is stopped from a remote maintenance PC', '• This information is logged when IP-GW is stopped from a remote maintenance PC'),
(327, 'IP-GW DSP failure', '• Optional service card malfunction: IP-GW16', '• Replace the corresponding optional service card'),
(328, 'The LoopBack(PRI23) was established by Network', 'This code means your network provider is diagnosing the PRI line', 'Consult your network provider'),
(329, 'Data Link failure', 'Data link between the PT and PBX failed', 'Check the connection between the PT and PBX'),
(330, 'Loop current detection error', '• Detection of LCOT loop current error', '• Change the corresponding trunk status back to In Service\n• Enter the feature number to clear Busy Out status\n• The trunk status is automatically changed back to In Service by system diagnosis performed at a preprogrammed time every day'),
(350, 'IP-unit SDRAM bit error', '• Optional service card malfunction: IP-EXT', '• The IP-EXT card will be rebooted automatically if the error is temporary\n• Replace the corresponding IP-EXT card if the card is not rebooted'),
(351, 'IP-unit download data check-sum error', '• Optional service card malfunction: IP-EXT', '• The IP-EXT card will be rebooted automatically if the error is temporary'),
(352, 'IP-unit DSP failure', '• Optional service card malfunction: IP-EXT', '• Replace the corresponding IP-EXT card if the card is not rebooted'),
(353, 'IP-PT DSP failure', '• IP-PT malfunction', '• The IP-PT will be rebooted automatically if the error is temporary\n• Replace the corresponding IP-PT if the IP-PT is not rebooted'),
(355, 'IP-unit alive check error', '• Optional service card malfunction: IP-EXT', '• The IP-EXT card will be rebooted automatically if the error is temporary\n• Replace the corresponding IP-EXT card if the card is not rebooted'),
(356, 'IP/TEL-unit communication error', '• Optional service card malfunction: IP-EXT', '• The IP-EXT card will be rebooted automatically if the error is temporary\n• Replace the corresponding IP-EXT card if the card is not rebooted'),
(357, 'IP-unit FLASH access error', '• Optional service card malfunction: IP-EXT', '• Replace the corresponding optional service card'),
(358, 'IP-unit boot error', '• Optional service card malfunction: IP-EXT', '• Replace the corresponding optional service card'),
(359, 'IP-unit DSP failure (boot diagnosis)', '• Optional service card malfunction: IP-EXT', '• Replace the corresponding optional service card'),
(360, 'IP-PT SUB-CPU failure', '• IP-PT malfunction', '• The IP-PT will be rebooted automatically if the error is temporary\n• Replace the corresponding IP-PT if the IP-PT is not rebooted'),
(361, 'IP-PT DHCP server no response', '• DHCP server is not active\n• Network malfunction', '• Consult your network administrator'),
(362, 'IP-PT Rebooted (cause DHCP server)', '• DHCP server is not active\n• Network malfunction\n• Network configuration has been changed', '• Consult your network administrator'),
(363, 'PPPoE connection was disconnected', 'Network malfunction', 'Consult your network administrator'),
(364, 'PPPoE authentication failure', '• Account information (ID, Password) that was entered is wrong\n• Network malfunction', '• Confirm account information (ID, Password)\n• Consult your network administrator'),
(365, 'SIP Server connection was disconnected', '• SIP server is not active\n• Network malfunction', 'Consult your network administrator'),
(366, 'SIP Server authentication failure', '• Account information (ID, Password) that was entered is wrong\n• Network malfunction', '• Confirm account information (ID, Password)\n• Consult your network administrator'),
(367, 'DDNS Server connection error', '• DDNS server is not active\n• Network malfunction', 'Consult your network administrator'),
(368, 'IP-CS synchronisation failure', 'The IP-CS is not synchronised withother CSs', '• Set an appropriate air synchronisation group\n• Refer to the Quick Installation Guide of the KX-NCP0158'),
(369, 'IP-CS Handover error', 'Master CS2 is not synchronised with Master CS1', '• Change the status of Master CS2 to OUS and then back to INS using theWEB-MC\n• Refer to the Quick Installation Guide of the KX-NCP0158'),
(370, 'IP-GW Rebooted by Maintenance Console', '• IP-GW is rebooted from a remote maintenance PC', '• This information is logged when IP-GW is rebooted from a remote maintenance PC'),
(371, 'IP-GW Rebooted', '• Optional service card malfunction: IP-GW', '• Check whether the software version of the IP-GW card is correct\n• Replace the corresponding optional service card'),
(372, 'NDSS message over IPGW notification - caused by IPGW Tx resource limitation', '• Optional service card malfunction: IP-GW', '• Ignore if not frequent\n• Change the IP-GW card status to Out of Service, and then back to In Service'),
(373, 'NDSS message over IPGW notification - caused by IPGW Rx resource limitation', '• Optional service card malfunction: IP-GW', '• Ignore if not frequent\n• Change the IP-GW card status to Out of Service, and then back to In Service'),
(374, 'NDSS message over IPGW notification - caused by shortage of IPGW resource', '• Optional service card malfunction: IP-GW', '• Ignore if not frequent\n• Change the IP-GW card status to Out of Service, and then back to In Service'),
(375, 'NDSS message over IPGW notification - caused by Network side', '• Network malfunction', '• Ignore if not frequent\n• Consult your network administrator'),
(379, 'IP-CS alive check error (Sync Master CS1)', '• IP-CS malfunction (Sync Master CS1)\n• Network malfunction', '• Check the network connections\n• Refer to the manual of the KX-NS0154.'),
(380, 'IP-CS alive check error (Sync Master CS2, Sync Slave)', '• IP-CS malfunction (Sync Master CS2, Sync Slave)\n• Network malfunction', '• Check the network connections\n• Refer to the manual of the KX-NS0154.'),
(381, 'IP-CS LAN Synchronisation Group limitation over', 'Wrong LAN Synchronisation Group setting', '• Set an appropriate LAN Synchronisation Group\n• Refer to the manual of the KX-NS0154'),
(382, 'IP-CS LAN connection check error', 'Network malfunction', '• Check the network connections\n• Refer to the manual of the KX-NS0154.'),
(383, 'IP-CS IGMP Querier\nnot found', 'Network malfunction', '• Check the network connections\n• Refer to the manual of the KX-NS0154'),
(390, 'Digital signal synchronization established', '• Synchronisation of digital line established or restored', '• This information is logged when synchronisation of digital line is established, and does not indicate an error condition that needs to be solved'),
(391, 'Data Link established', '• Connection with PC Phone/PC Console or Voice Processing System (DPT Integration) established or restored', '• This information is logged when connection with PC Phone/PC Console or Voice Processing System (DPT Integration) is established, and does not indicate an error condition that needs to be solved. However, if this is logged frequently (with \"305 Data Link failure\"), check the connection as it may not be done properly.'),
(392, 'Clock master card selected', '• Clock master card has been changed to the one indicated by the sub code', '• Check if the proper card is selected as the new clock master card'),
(393, 'LAN Carrier detected', '• IP-GW card connected to the LAN', '• This information is logged when synchronisation of LAN is established'),
(394, 'IP-GW Core Data Link established', '• IP-GW Core Data Link established', '• This information is logged when IP-GW Core Data Link is recovered'),
(395, 'IP-GW Gatekeeper Error Cleared', '• Connection to the Gatekeeper is recovered', '• This information is logged when connection to the Gatekeeper is recovered'),
(396, 'IP-GW Run', '• IP-GW is started from a remote maintenance PC', '• This information is logged when IP-GW is started from a remote maintenance PC'),
(397, 'The LoopBack(PRI23) was canceled by Network', 'This code means your network provider has finished the diagnosis of the PRI line', 'This code means your network provider has finished the diagnosis of the PRI line'),
(510, 'SMDR disconnect', '• RS-232C cable not connected\n• Breaking of RS-232C cable\n• Printer (terminal equipment) malfunction', '• Check the RS-232C cable\n• Check the terminal equipment'),
(520, 'CTI link failure', '• RS-232C/LAN cable not connected\n• CTI-Application malfunction', '• Check the RS-232C/LAN cable\n• Check the CTI-Application'),
(521, 'CTI Monitor stopped', 'Connections with PC Consoles or CTI-Applications have been disconnected because of high-load.', 'Try connecting to the PBX later'),
(522, 'PC Console disconnected', 'Connections with PC Consoles or CTI-Applications have been disconnected because of high-load.', 'Try connecting to the PBX later'),
(523, 'First Party CTI disconnected', 'Connections with PC Consoles or CTI-Applications have been disconnected because of high-load.', 'Try connecting to the PBX later'),
(524, 'CT Link disconnected (No memory)', 'Connections with PC Consoles or CTI-Applications have been disconnected because of high-load.', 'Try connecting to the PBX later'),
(525, 'CT Link no response', '• RS-232C/LAN cable not connected\n• CTI-Application malfunction', '• Check the RS-232C/LAN cable\n• Check the CTI-Application'),
(526, 'CT Link disconnected (No response)', '• RS-232C/LAN cable not connected\n• CTI-Application malfunction', '• Check the RS-232C/LAN cable\n• Check the CTI-Application'),
(527, 'CDR retry out', '• RS-232C/LAN cable not connected\n• CTI-Application malfunction', '• Check the RS-232C/LAN cable\n• Check the CTI-Application'),
(528, 'Multi site start up error (SD card not installed)', 'SD card is not installed when the Multi site connection is starting up', '• Confirm that the SD card is properly installed\n• Replace the SD card'),
(529, 'Multi site alive check retry', 'Communication status between sites is unstable', '• Check all cable connections between the sites, and check that hubs, routers, etc. are operating correctly\n• Confirm that the communication transmission speed between sites is sufficient\n• Confirm that all other partiesُ equipment is powered on\n• Consult your network administrator'),
(530, 'Multi site connection error', 'A communication error between sites is occurring', '• Check all cable connections between the sites, and check that hubs, routers, etc. are operating correctly\n• Confirm that the communication transmission speed between sites is sufficient\n• Confirm that all other partiesُ equipment is powered on\n• Consult your network administrator'),
(531, 'Multi site connection recovery', 'A communication error between sites was recovered from', '• Check all cable connections between the sites, and check that hubs, routers, etc. are operating correctly\n• Confirm that the communication transmission speed between sites is sufficient\n• Confirm that all other partiesُ equipment is powered on\n• Consult your network administrator'),
(532, 'Multi site data sync error', 'A communication error between sites is occurring', '• Check all cable connections between the sites, and check that hubs, routers, etc. are operating correctly\n• Confirm that all other partiesُ equipment is powered on\n• Consult your network administrator'),
(533, 'Unit start up error', 'A communication error between sites is occurring when the PBX is starting up', '• Check all cable connections between the sites, and check that hubs, routers, etc. are operating correctly\n• Confirm that all other partiesُ equipment is powered on\n• Consult your network administrator'),
(534, 'Unit registration denied', 'Due to software version mismatch between Master unit and Slave unit to be registered, site registration has been failed', 'Upgrade the software of either Master unit or Slave unit, and make both software version same. (In case of Slave unit, please login the Slave unit directly and upgrade the software)'),
(535, 'Change into Backup Master mode', None, None),
(536, 'Backup Master mode was released', 'Backup Master mode was released.', 'This message shows that the operation mode recovered from Backup Master mode.'),
(537, 'Change into Isolated mode', '• Malfunction occurred in Master unit or Backup Master unit.\n• Malfunction occurred in the communication path of Slave unit.', '• Check error log of Master unit or Backup Master unit.\n• Check all cable connections between the sites, and check that hubs, routers, etc. are operating correctly\n• Confirm that the communication transmission speed between sites is sufficient\n• Confirm that all other partiesُ equipment is powered on\n• Consult your network administrator'),
(538, 'Isolated mode was released', 'Isolated mode was released.', 'This message shows that the operation mode recovered from Isolated mode.'),
(539, 'VPN error', 'A communication error is occurring in VPN.', '• Check all cable connections between PBX and the other equipment connected via VPN, and check that hubs, routers, etc. are operating correctly\n• Confirm that the communication transmission speed between PBX and the other equipment connected via VPN is sufficient\n• Confirm that all other partiesُ equipment is powered on\n• Consult your network administrator'),
(540, 'Network Security Alarm', 'Security issue such as DOS attacks occurred.', 'Consult your network administrator'),
(541, 'NAS disconnected', '• NAS is not active\n• Network malfunction', '• Check all cable connections between the PBX and the NAS, and check that hubs, routers, etc. are operating correctly\n• Confirm that the communication transmission speed between the PBX and the NAS is sufficient\n• Confirm that all other equipment is powered on\n• Consult your network administrator'),
(542, 'Not enough free space on NAS', '• Not enough memory space available to save the data\n• Wrong permission of the NAS', '• Remove unnecessary files from the NAS\n• Check the permission of the NAS')
]

    for error in errors:
        error_code_num, error_message, probable_cause, solution = error
        Errors.objects.get_or_create(
            errorcodenum=error_code_num,
            defaults={
                "errormessage": error_message,
                "probablecause": probable_cause,
                "solution": solution,
            },
        )

    telephones = [(1, None, 'آذربایجان', '+994', datetime.datetime(2025, 3, 6, 7, 58, 58, 880741, tzinfo=datetime.timezone.utc), None), (2, None, 'آرژانتین', '+54', datetime.datetime(2025, 3, 6, 7, 58, 58, 973962, tzinfo=datetime.timezone.utc), None), (3, None, 'آفریقای جنوبی', '+27', datetime.datetime(2025, 3, 6, 7, 58, 58, 975968, tzinfo=datetime.timezone.utc), None), (4, None, 'آفریقای مرکزی', '+236', datetime.datetime(2025, 3, 6, 7, 58, 58, 977962, tzinfo=datetime.timezone.utc), None), (5, None, 'آلبانی', '+355', datetime.datetime(2025, 3, 6, 7, 58, 58, 978963, tzinfo=datetime.timezone.utc), None), (6, None, 'آلمان', '+49', datetime.datetime(2025, 3, 6, 7, 58, 58, 979643, tzinfo=datetime.timezone.utc), None), (7, None, 'آمریکا', '+1', datetime.datetime(2025, 3, 6, 7, 58, 58, 980655, tzinfo=datetime.timezone.utc), None), (8, None, 'آنگولا', '+244', datetime.datetime(2025, 3, 6, 7, 58, 58, 981804, tzinfo=datetime.timezone.utc), None), (9, None, 'اتریش', '+43', datetime.datetime(2025, 3, 6, 7, 58, 58, 983406, tzinfo=datetime.timezone.utc), None), (10, None, 'اتیوپی', '+251', datetime.datetime(2025, 3, 6, 7, 58, 58, 984412, tzinfo=datetime.timezone.utc), None), (11, None, 'اردن', '+962', datetime.datetime(2025, 3, 6, 7, 58, 58, 985408, tzinfo=datetime.timezone.utc), None), (12, None, 'ارمنستان', '+374', datetime.datetime(2025, 3, 6, 7, 58, 58, 986407, tzinfo=datetime.timezone.utc), None), (13, None, 'اروگوئه', '+598', datetime.datetime(2025, 3, 6, 7, 58, 58, 988409, tzinfo=datetime.timezone.utc), None), (14, None, 'اریتره', '+291', datetime.datetime(2025, 3, 6, 7, 58, 58, 989250, tzinfo=datetime.timezone.utc), None), (15, None, 'ازبکستان', '+7371', datetime.datetime(2025, 3, 6, 7, 58, 58, 990396, tzinfo=datetime.timezone.utc), None), (16, None, 'اسپانیا', '+34', datetime.datetime(2025, 3, 6, 7, 58, 58, 991401, tzinfo=datetime.timezone.utc), None), (17, None, 'استرالیا', '+61', datetime.datetime(2025, 3, 6, 7, 58, 58, 992400, tzinfo=datetime.timezone.utc), None), (18, None, 'استونی', '+372', datetime.datetime(2025, 3, 6, 7, 58, 58, 994398, tzinfo=datetime.timezone.utc), None), (19, None, 'افغانستان', '+93', datetime.datetime(2025, 3, 6, 7, 58, 58, 995347, tzinfo=datetime.timezone.utc), None), (20, None, 'الجزایر', '+213', datetime.datetime(2025, 3, 6, 7, 58, 58, 996351, tzinfo=datetime.timezone.utc), None), (21, None, 'السالوادور', '+503', datetime.datetime(2025, 3, 6, 7, 58, 58, 999346, tzinfo=datetime.timezone.utc), None), (22, None, 'امارات', '+971', datetime.datetime(2025, 3, 6, 7, 58, 59, 1347, tzinfo=datetime.timezone.utc), None), (23, None, 'اندونزی', '+62', datetime.datetime(2025, 3, 6, 7, 58, 59, 3345, tzinfo=datetime.timezone.utc), None), (24, None, 'انگلستان', '+44', datetime.datetime(2025, 3, 6, 7, 58, 59, 7343, tzinfo=datetime.timezone.utc), None), (25, None, 'اوگاندا', '+256', datetime.datetime(2025, 3, 6, 7, 58, 59, 8342, tzinfo=datetime.timezone.utc), None), (26, None, 'اوکراین', '+380', datetime.datetime(2025, 3, 6, 7, 58, 59, 10344, tzinfo=datetime.timezone.utc), None), (27, None, 'ایران', '+98', datetime.datetime(2025, 3, 6, 7, 58, 59, 11343, tzinfo=datetime.timezone.utc), None), (28, None, 'ایرلند', '+353', datetime.datetime(2025, 3, 6, 7, 58, 59, 14392, tzinfo=datetime.timezone.utc), None), (29, None, 'اکوادور', '+593', datetime.datetime(2025, 3, 6, 7, 58, 59, 15426, tzinfo=datetime.timezone.utc), None), (30, None, 'ایسلند', '+354', datetime.datetime(2025, 3, 6, 7, 58, 59, 16775, tzinfo=datetime.timezone.utc), None), (31, None, 'باربادوس', '+18094', datetime.datetime(2025, 3, 6, 7, 58, 59, 18020, tzinfo=datetime.timezone.utc), None), (32, None, 'باهاما', '+18093', datetime.datetime(2025, 3, 6, 7, 58, 59, 19022, tzinfo=datetime.timezone.utc), None), (33, None, 'بحرین', '+973', datetime.datetime(2025, 3, 6, 7, 58, 59, 20024, tzinfo=datetime.timezone.utc), None), (34, None, 'برزیل', '+55', datetime.datetime(2025, 3, 6, 7, 58, 59, 21021, tzinfo=datetime.timezone.utc), None), (35, None, 'برونئی', '+673', datetime.datetime(2025, 3, 6, 7, 58, 59, 22626, tzinfo=datetime.timezone.utc), None), (36, None, 'بلژیک', '+32', datetime.datetime(2025, 3, 6, 7, 58, 59, 23633, tzinfo=datetime.timezone.utc), None), (37, None, 'بلغارستان', '+359', datetime.datetime(2025, 3, 6, 7, 58, 59, 24630, tzinfo=datetime.timezone.utc), None), (38, None, 'بنگلادش', '+880', datetime.datetime(2025, 3, 6, 7, 58, 59, 26136, tzinfo=datetime.timezone.utc), None), (39, None, 'بنین', '+229', datetime.datetime(2025, 3, 6, 7, 58, 59, 27139, tzinfo=datetime.timezone.utc), None), (40, None, 'بوتان', '+267', datetime.datetime(2025, 3, 6, 7, 58, 59, 29137, tzinfo=datetime.timezone.utc), None), (41, None, 'بوسنی و هرزگوین', '+387', datetime.datetime(2025, 3, 6, 7, 58, 59, 30139, tzinfo=datetime.timezone.utc), None), (42, None, 'بولیوی', '+591', datetime.datetime(2025, 3, 6, 7, 58, 59, 31677, tzinfo=datetime.timezone.utc), None), (43, None, 'پاراگوئه', '+595', datetime.datetime(2025, 3, 6, 7, 58, 59, 32683, tzinfo=datetime.timezone.utc), None), (44, None, 'پاناما', '+507', datetime.datetime(2025, 3, 6, 7, 58, 59, 33682, tzinfo=datetime.timezone.utc), None), (45, None, 'پاکستان', '+92', datetime.datetime(2025, 3, 6, 7, 58, 59, 34681, tzinfo=datetime.timezone.utc), None), (46, None, 'پرتغال', '+351', datetime.datetime(2025, 3, 6, 7, 58, 59, 35681, tzinfo=datetime.timezone.utc), None), (47, None, 'پرو', '+51', datetime.datetime(2025, 3, 6, 7, 58, 59, 36680, tzinfo=datetime.timezone.utc), None), (48, None, 'پنال', '+95', datetime.datetime(2025, 3, 6, 7, 58, 59, 37679, tzinfo=datetime.timezone.utc), None), (49, None, 'تاجیکستان', '+992', datetime.datetime(2025, 3, 6, 7, 58, 59, 38679, tzinfo=datetime.timezone.utc), None), (50, None, 'تانزانیا', '+255', datetime.datetime(2025, 3, 6, 7, 58, 59, 39679, tzinfo=datetime.timezone.utc), None), (51, None, 'تایلند', '+66', datetime.datetime(2025, 3, 6, 7, 58, 59, 40678, tzinfo=datetime.timezone.utc), None), (52, None, 'تایوان', '+886', datetime.datetime(2025, 3, 6, 7, 58, 59, 42676, tzinfo=datetime.timezone.utc), None), (53, None, 'ترینیداد', '+1868', datetime.datetime(2025, 3, 6, 7, 58, 59, 43675, tzinfo=datetime.timezone.utc), None), (54, None, 'ترکمنستان', '+7363', datetime.datetime(2025, 3, 6, 7, 58, 59, 45676, tzinfo=datetime.timezone.utc), None), (55, None, 'ترکیه', '+90', datetime.datetime(2025, 3, 6, 7, 58, 59, 46673, tzinfo=datetime.timezone.utc), None), (56, None, 'تونس', '+216', datetime.datetime(2025, 3, 6, 7, 58, 59, 47678, tzinfo=datetime.timezone.utc), None), (57, None, 'تونگا', '+676', datetime.datetime(2025, 3, 6, 7, 58, 59, 48745, tzinfo=datetime.timezone.utc), None), (58, None, 'جامائیکا', '+18099', datetime.datetime(2025, 3, 6, 7, 58, 59, 49751, tzinfo=datetime.timezone.utc), None), (59, None, 'جزایر مارشال', '+692', datetime.datetime(2025, 3, 6, 7, 58, 59, 50808, tzinfo=datetime.timezone.utc), None), (60, None, 'چاد', '+237', datetime.datetime(2025, 3, 6, 7, 58, 59, 51813, tzinfo=datetime.timezone.utc), None), (61, None, 'چین', '+86', datetime.datetime(2025, 3, 6, 7, 58, 59, 52810, tzinfo=datetime.timezone.utc), None), (62, None, 'چک', '+42', datetime.datetime(2025, 3, 6, 7, 58, 59, 53810, tzinfo=datetime.timezone.utc), None), (63, None, 'دانمارک', '+45', datetime.datetime(2025, 3, 6, 7, 58, 59, 54809, tzinfo=datetime.timezone.utc), None), (64, None, 'دومینیکا', '+508', datetime.datetime(2025, 3, 6, 7, 58, 59, 55807, tzinfo=datetime.timezone.utc), None), (65, None, 'دومینیکن', '+1809', datetime.datetime(2025, 3, 6, 7, 58, 59, 56809, tzinfo=datetime.timezone.utc), None), (66, None, 'رواندا', '+250', datetime.datetime(2025, 3, 6, 7, 58, 59, 57808, tzinfo=datetime.timezone.utc), None), (67, None, 'روسیه', '+7095', datetime.datetime(2025, 3, 6, 7, 58, 59, 59806, tzinfo=datetime.timezone.utc), None), (68, None, 'رومانی', '+40', datetime.datetime(2025, 3, 6, 7, 58, 59, 60806, tzinfo=datetime.timezone.utc), None), (69, None, 'زئیر', '+243', datetime.datetime(2025, 3, 6, 7, 58, 59, 61805, tzinfo=datetime.timezone.utc), None), (70, None, 'زامبیا', '+260', datetime.datetime(2025, 3, 6, 7, 58, 59, 63803, tzinfo=datetime.timezone.utc), None), (71, None, 'زلاندنو', '+64', datetime.datetime(2025, 3, 6, 7, 58, 59, 64802, tzinfo=datetime.timezone.utc), None), (72, None, 'زیمبابوه', '+263', datetime.datetime(2025, 3, 6, 7, 58, 59, 65802, tzinfo=datetime.timezone.utc), None), (73, None, 'ژاپن', '+81', datetime.datetime(2025, 3, 6, 7, 58, 59, 66801, tzinfo=datetime.timezone.utc), None), (74, None, 'ساحل عاج', '+225', datetime.datetime(2025, 3, 6, 7, 58, 59, 67801, tzinfo=datetime.timezone.utc), None), (75, None, 'سنگال', '+221', datetime.datetime(2025, 3, 6, 7, 58, 59, 68800, tzinfo=datetime.timezone.utc), None), (76, None, 'سوئد', '+46', datetime.datetime(2025, 3, 6, 7, 58, 59, 69799, tzinfo=datetime.timezone.utc), None), (77, None, 'سوئیس', '+41', datetime.datetime(2025, 3, 6, 7, 58, 59, 70799, tzinfo=datetime.timezone.utc), None), (78, None, 'سودان', '+249', datetime.datetime(2025, 3, 6, 7, 58, 59, 71798, tzinfo=datetime.timezone.utc), None), (79, None, 'سورینام', '+597', datetime.datetime(2025, 3, 6, 7, 58, 59, 72798, tzinfo=datetime.timezone.utc), None), (80, None, 'سوریه', '+963', datetime.datetime(2025, 3, 6, 7, 58, 59, 73797, tzinfo=datetime.timezone.utc), None), (81, None, 'سومالی', '+252', datetime.datetime(2025, 3, 6, 7, 58, 59, 75798, tzinfo=datetime.timezone.utc), None), (82, None, 'سیشل', '+148', datetime.datetime(2025, 3, 6, 7, 58, 59, 77228, tzinfo=datetime.timezone.utc), None), (83, None, 'شیلی', '+56', datetime.datetime(2025, 3, 6, 7, 58, 59, 78232, tzinfo=datetime.timezone.utc), None), (84, None, 'صربستان و مونته نگرو', '+381', datetime.datetime(2025, 3, 6, 7, 58, 59, 79229, tzinfo=datetime.timezone.utc), None), (85, None, 'عربستان', '+996', datetime.datetime(2025, 3, 6, 7, 58, 59, 80229, tzinfo=datetime.timezone.utc), None), (86, None, 'عمان', '+968', datetime.datetime(2025, 3, 6, 7, 58, 59, 81230, tzinfo=datetime.timezone.utc), None), (87, None, 'غنا', '+233', datetime.datetime(2025, 3, 6, 7, 58, 59, 82229, tzinfo=datetime.timezone.utc), None), (88, None, 'فرانسه', '+33', datetime.datetime(2025, 3, 6, 7, 58, 59, 83228, tzinfo=datetime.timezone.utc), None), (89, None, 'فیجی', '+679', datetime.datetime(2025, 3, 6, 7, 58, 59, 84228, tzinfo=datetime.timezone.utc), None), (90, None, 'فیلیپین', '+63', datetime.datetime(2025, 3, 6, 7, 58, 59, 85227, tzinfo=datetime.timezone.utc), None), (91, None, 'قبرس', '+357', datetime.datetime(2025, 3, 6, 7, 58, 59, 86227, tzinfo=datetime.timezone.utc), None), (92, None, 'قرقیزستان', '+7331', datetime.datetime(2025, 3, 6, 7, 58, 59, 87226, tzinfo=datetime.timezone.utc), None), (93, None, 'قطر', '+974', datetime.datetime(2025, 3, 6, 7, 58, 59, 88226, tzinfo=datetime.timezone.utc), None), (94, None, 'کامبوج', '+855', datetime.datetime(2025, 3, 6, 7, 58, 59, 89225, tzinfo=datetime.timezone.utc), None), (95, None, 'کره جنوبی', '+82', datetime.datetime(2025, 3, 6, 7, 58, 59, 92222, tzinfo=datetime.timezone.utc), None), (96, None, 'کره شمالی', '+850', datetime.datetime(2025, 3, 6, 7, 58, 59, 93463, tzinfo=datetime.timezone.utc), None), (97, None, 'کرواسی', '+385', datetime.datetime(2025, 3, 6, 7, 58, 59, 94467, tzinfo=datetime.timezone.utc), None), (98, None, 'کاستاریکا', '+506', datetime.datetime(2025, 3, 6, 7, 58, 59, 95466, tzinfo=datetime.timezone.utc), None), (99, None, 'کلمبیا', '+57', datetime.datetime(2025, 3, 6, 7, 58, 59, 96466, tzinfo=datetime.timezone.utc), None), (100, None, 'کنگو', '+242', datetime.datetime(2025, 3, 6, 7, 58, 59, 97466, tzinfo=datetime.timezone.utc), None), (101, None, 'کنیا', '+254', datetime.datetime(2025, 3, 6, 7, 58, 59, 98464, tzinfo=datetime.timezone.utc), None), (102, None, 'کوبا', '+53', datetime.datetime(2025, 3, 6, 7, 58, 59, 99464, tzinfo=datetime.timezone.utc), None), (103, None, 'کویت', '+965', datetime.datetime(2025, 3, 6, 7, 58, 59, 100464, tzinfo=datetime.timezone.utc), None), (104, None, 'گابن', '+241', datetime.datetime(2025, 3, 6, 7, 58, 59, 101468, tzinfo=datetime.timezone.utc), None), (105, None, 'گامبیا', '+220', datetime.datetime(2025, 3, 6, 7, 58, 59, 102468, tzinfo=datetime.timezone.utc), None), (106, None, 'گرجستان', '+788', datetime.datetime(2025, 3, 6, 7, 58, 59, 103467, tzinfo=datetime.timezone.utc), None), (107, None, 'گرنادا', '+18083', datetime.datetime(2025, 3, 6, 7, 58, 59, 104468, tzinfo=datetime.timezone.utc), None), (108, None, 'گواتمالا', '+502', datetime.datetime(2025, 3, 6, 7, 58, 59, 106467, tzinfo=datetime.timezone.utc), None), (109, None, 'گویان', '+592', datetime.datetime(2025, 3, 6, 7, 58, 59, 107465, tzinfo=datetime.timezone.utc), None), (110, None, 'گینه نو', '+675', datetime.datetime(2025, 3, 6, 7, 58, 59, 109274, tzinfo=datetime.timezone.utc), None), (111, None, 'گینه کوناکری', '+224', datetime.datetime(2025, 3, 6, 7, 58, 59, 110278, tzinfo=datetime.timezone.utc), None), (112, None, 'گینه بیسائو', '+245', datetime.datetime(2025, 3, 6, 7, 58, 59, 111277, tzinfo=datetime.timezone.utc), None), (113, None, 'لبنان', '+961', datetime.datetime(2025, 3, 6, 7, 58, 59, 112277, tzinfo=datetime.timezone.utc), None), (114, None, 'لسوتو', '+266', datetime.datetime(2025, 3, 6, 7, 58, 59, 113276, tzinfo=datetime.timezone.utc), None), (115, None, 'لتونی', '+371', datetime.datetime(2025, 3, 6, 7, 58, 59, 114275, tzinfo=datetime.timezone.utc), None), (116, None, 'لائوس', '+856', datetime.datetime(2025, 3, 6, 7, 58, 59, 115274, tzinfo=datetime.timezone.utc), None), (117, None, 'لهستان', '+48', datetime.datetime(2025, 3, 6, 7, 58, 59, 116275, tzinfo=datetime.timezone.utc), None), (118, None, 'لیبریا', '+231', datetime.datetime(2025, 3, 6, 7, 58, 59, 117274, tzinfo=datetime.timezone.utc), None), (119, None, 'لیبی', '+218', datetime.datetime(2025, 3, 6, 7, 58, 59, 118273, tzinfo=datetime.timezone.utc), None), (120, None, 'لیتوانی', '+370', datetime.datetime(2025, 3, 6, 7, 58, 59, 119272, tzinfo=datetime.timezone.utc), None), (121, None, 'مالاوی', '+265', datetime.datetime(2025, 3, 6, 7, 58, 59, 120274, tzinfo=datetime.timezone.utc), None), (122, None, 'مالزی', '+60', datetime.datetime(2025, 3, 6, 7, 58, 59, 122271, tzinfo=datetime.timezone.utc), None), (123, None, 'مالدیو', '+960', datetime.datetime(2025, 3, 6, 7, 58, 59, 123270, tzinfo=datetime.timezone.utc), None), (124, None, 'مالی', '+223', datetime.datetime(2025, 3, 6, 7, 58, 59, 125269, tzinfo=datetime.timezone.utc), None), (125, None, 'مجارستان', '+36', datetime.datetime(2025, 3, 6, 7, 58, 59, 127478, tzinfo=datetime.timezone.utc), None), (126, None, 'مراکش', '+212', datetime.datetime(2025, 3, 6, 7, 58, 59, 128482, tzinfo=datetime.timezone.utc), None), (127, None, 'مصر', '+20', datetime.datetime(2025, 3, 6, 7, 58, 59, 130482, tzinfo=datetime.timezone.utc), None), (128, None, 'مکزیک', '+52', datetime.datetime(2025, 3, 6, 7, 58, 59, 131480, tzinfo=datetime.timezone.utc), None), (129, None, 'مغولستان', '+976', datetime.datetime(2025, 3, 6, 7, 58, 59, 132480, tzinfo=datetime.timezone.utc), None), (130, None, 'مولداوی', '+373', datetime.datetime(2025, 3, 6, 7, 58, 59, 133479, tzinfo=datetime.timezone.utc), None), (131, None, 'موناکو', '+377', datetime.datetime(2025, 3, 6, 7, 58, 59, 134673, tzinfo=datetime.timezone.utc), None), (132, None, 'موریتانی', '+222', datetime.datetime(2025, 3, 6, 7, 58, 59, 135679, tzinfo=datetime.timezone.utc), None), (133, None, 'موریس', '+230', datetime.datetime(2025, 3, 6, 7, 58, 59, 137675, tzinfo=datetime.timezone.utc), None), (134, None, 'موزامبیک', '+258', datetime.datetime(2025, 3, 6, 7, 58, 59, 138676, tzinfo=datetime.timezone.utc), None), (135, None, 'نامبیا', '+264', datetime.datetime(2025, 3, 6, 7, 58, 59, 139676, tzinfo=datetime.timezone.utc), None), (136, None, 'نپال', '+977', datetime.datetime(2025, 3, 6, 7, 58, 59, 140675, tzinfo=datetime.timezone.utc), None), (137, None, 'نروژ', '+47', datetime.datetime(2025, 3, 6, 7, 58, 59, 141675, tzinfo=datetime.timezone.utc), None), (138, None, 'نیجر', '+227', datetime.datetime(2025, 3, 6, 7, 58, 59, 143675, tzinfo=datetime.timezone.utc), None), (139, None, 'نیجریه', '+234', datetime.datetime(2025, 3, 6, 7, 58, 59, 144671, tzinfo=datetime.timezone.utc), None), (140, None, 'نیکاراگوئه', '+505', datetime.datetime(2025, 3, 6, 7, 58, 59, 145672, tzinfo=datetime.timezone.utc), None), (141, None, 'هائیتی', '+509', datetime.datetime(2025, 3, 6, 7, 58, 59, 146669, tzinfo=datetime.timezone.utc), None), (142, None, 'هلند', '+31', datetime.datetime(2025, 3, 6, 7, 58, 59, 147670, tzinfo=datetime.timezone.utc), None), (143, None, 'هند', '+91', datetime.datetime(2025, 3, 6, 7, 58, 59, 148669, tzinfo=datetime.timezone.utc), None), (144, None, 'هندوراس', '+504', datetime.datetime(2025, 3, 6, 7, 58, 59, 149669, tzinfo=datetime.timezone.utc), None), (145, None, 'هنگ کنگ', '+852', datetime.datetime(2025, 3, 6, 7, 58, 59, 150667, tzinfo=datetime.timezone.utc), None), (146, None, 'یونان', '+30', datetime.datetime(2025, 3, 6, 7, 58, 59, 151666, tzinfo=datetime.timezone.utc), None)]

    for item in telephones:
        _, type, name, code, _, _ = item
        
        Telephons.objects.get_or_create(
            code=code,
            defaults={
                'type': type,
                'name': name,
            }
        )

    get, created = Groups.objects.get_or_create(enname="supporter", pename="پشتیبانی")
    Groups.objects.get_or_create(enname="superadmin", pename="ابر مدیر")
    Groups.objects.get_or_create(enname="admin", pename="مدیر")
    Groups.objects.get_or_create(enname="member", pename="کاربر")

    getSup, createdSup = Users.objects.get_or_create(username="supporter", defaults={
        'password': make_password("DLqyS!5#dF13"),
        'group': get,
        'groupname': get.enname,
        'extension': -1,
        'lastname': 'الوند',
        'name': 'پشتیبانی',
        'email': '<EMAIL>',
        'email_verified_at': timezone.now()
    })

    Infos.objects.get_or_create(user=getSup, defaults={
        'birthdate': '2025/03/25',
        'phonenumber': '***********',
        'telephone': '********',
        'province': '10',
        'city': 'مشهد',
        'address': 'فرهنگ',
        'gender': '2',
        'military': '4',
        'maritalstatus': '1',
        'educationdegree': '6',
        'educationfield': 'IoT',
        'cardnumber': '****************',
        'accountnumber': '1234567',
        'accountnumbershaba': '640120020000009520896080',
        'nationalcode': '**********'
    })

    Permissions.objects.get_or_create(user=getSup, defaults={
        'perm_email': True,
        'can_view': True,
        'can_write': True,
        'can_modify': True,
        'can_delete': True,
        'errorsreport': True
    })

def checkLicense():
    check = lices.objects.all()
    if check.exists() and not check.first().active:
        return False
    return True

def getVersion():
    ver = lices.objects.all()
    if not ver.exists(): return None
    return getTupleIndex(VERSIONS, ver.first().version)

def getInfosOfUserByUsername(username, value):
    if not username:
        return None
    user = Users.objects.filter(username__iexact=username)
    if not user.exists():
        return None
    infos = Infos.objects.filter(user=user.first())
    if not infos.exists(): return None
    if not value.lower() in [field.name for field in Infos._meta.fields]: return None
    return next(iter(infos.values(value).first().values()))

def getHWID():
    pythoncom.CoInitialize()
    system = wmi.WMI()
    return system.Win32_ComputerSystemProduct()[0].UUID if system else None


def validatePhotoExt(filename):
    try:
        name, ext = os.path.splitext(filename)
        if ext.lower() in [".png", ".jpg", ".jpeg"]:
            return ext
    except:
        return False


def isInternational(prefix):
    if Telephons.objects.filter(code__contains=prefix).exists():
        return True
    return False


def calculatePrice(duration: str, price: int) -> int:
    if not duration or price is None or price <= 0: return 0
    hour, minute, second = map(int, duration.split(":"))
    toSeconds = (hour * 3600) + (minute * 60) + second
    toMinutes = math.ceil(toSeconds / 60)
    return toMinutes * price


def persianCallTypeToEnglish(ct):
    callType = []
    if "همه تماس ها" in ct:
        return ['incomingNA', 'incomingRC', 'incomingAN', "Transfer", "incomingDISA", 'incomingHangUp', 'outGoing',
                'Extension']
    if "تماس های پاسخ نداده شده" in ct:
        if "incomingNA" not in callType: callType.append("incomingNA")
    if "تماس های ورودی" in ct:
        for item in ['incomingNA', 'incomingRC', 'incomingAN', "Transfer", "incomingDISA", 'incomingHangUp']:
            if item not in callType:
                callType.append(item)
    if "تماس های خروجی" in ct:
        if "outGoing" not in callType: callType.append("outGoing")
    if "تماس های داخلی" in ct:
        if "Extension" not in callType: callType.append("Extension")
    return callType


def getUrbanlinesThatUserAccessThem(username):
    if not username: return None
    user = Users.objects.filter(username__iexact=username)
    if not user.exists(): return None
    allExts = []
    ext = user.first().extension
    if ext:
        allExts.append(ext)
    userexts = user.first().usersextension
    if userexts:
        for userext in userexts:
            if userext: allExts.append(userext)
    labels = Permissions.objects.filter(user=user.first())
    if labels.exists() and labels.first().exts_label:
        extgps = Extensionsgroups.objects.filter(label__in=labels.first().exts_label)
        if extgps.exists():
            for item in extgps:
                allExts = allExts + item.exts
    urbans = [x.urbanline for x in Records.objects.filter(extension__in=allExts) if x.urbanline]
    return sorted(set(urbans))

def getExtensionlines(username):
    if not username: return None
    user = Users.objects.filter(username__iexact=username)
    if not user.exists(): return None
    allExtentions = []
    ext = user.first().extension
    if ext:
        allExtentions.append(ext)
    userexts = user.first().usersextension
    if userexts:
        for userext in userexts:
            if userext: allExtentions.append(userext)
    labels = Permissions.objects.filter(user=user.first())
    if labels.exists() and labels.first().exts_label:
        extGroup = Extensionsgroups.objects.filter(label__in=labels.first().exts_label)
        if extGroup.exists():
            for item in extGroup:
                allExtentions = allExtentions + item.exts
    extensions = [x.extension for x in Records.objects.filter(extension__in=allExtentions) if x.extension]
    return sorted(set(extensions))

def dashboardData(username):
    if not username: return []
    user = Users.objects.filter(username__iexact=username)
    if not user.exists(): return []
    allExtentions = []
    ext = user.first().extension
    if ext:
        allExtentions.append(ext)
    userexts = user.first().usersextension
    if userexts:
        for userext in userexts:
            if userext: allExtentions.append(userext)
    labels = Permissions.objects.filter(user=user.first())
    if labels.exists() and labels.first().exts_label:
        extGroup = Extensionsgroups.objects.filter(label__in=labels.first().exts_label)
        if extGroup.exists():
            for item in extGroup:
                allExtentions = allExtentions + item.exts
    extensions = [x for x in Records.objects.filter(extension__in=allExtentions).order_by('-created_at')]
    return extensions

def getPrice(which):
    if not which: return None
    price = None
    cost = Costs.objects.all()
    match which.lower():
        case "irancell":
            if cost.exists():
                price = cost.first().irancell
        case "hamrahaval":
            if cost.exists():
                price = cost.first().hamrahaval
        case "rightel":
            if cost.exists():
                price = cost.first().rightel
        case "provincial":
            if cost.exists():
                price = cost.first().provincial
        case "international":
            if cost.exists():
                price = cost.first().international
        case "outofprovincial":
            if cost.exists():
                price = cost.first().outofprovincial
        case _:
            return None
    if not price: return None
    return price


def callTypeDetector(number):
    number = str(number)
    if len(number) < 5:
        return None
    if number[0:3] == "+98":
        number = number.replace("+98", "0")
    if number[0:3] == "000":
        number = number[3:]
    if number[0:2] == "98":
        if number[2:4] != "09":
            number = "09" + number[4:]
        else:
            number = "0" + number[2:]
    if number[0:4] == "0098":
        number = "0" + number[4:]
    hamrahaval = ["0910", "0910", "0912", "0913", "0914", "0915", "0916", "0917", "0918", "0919", "0991", "0990",
                  "0992", "0993", "0996"]
    irancell = ["0900", "0901", "0902", "0903", "0904", "0905", "0930", "0933", "0935", "0936", "0937", "0938", "0939",
                "0941"]
    rightel = ["0921", "0922", "0923", "0920"]
    outofprovincial = ["041", "044", "045", "031", "084", "077", "021", "038", "051", "056", "058", "061", "024", "023",
                       "054", "071", "026", "025", "028", "087", "034", "083", "074", "017", "013", "066", "011", "086",
                       "076", "081", "035"]
    return "irancell" if number[0:4] in irancell else "hamrahaval" if number[
                                                                      0:4] in hamrahaval else "rightel" if number[
                                                                                                           0:4] in rightel else "international" if isInternational(
        number[0:3]) else "provincial" if int(number[0]) in range(1, 9) else "outofprovincial" if number[
                                                                                                  0:3] in outofprovincial else None


def isfloat(value):
    try:
        float(value)
        return True
    except:
        return False


def getRandomCode():
    rand = random.randint(100000, 999999)
    if not Verifications.objects.filter(code=rand).exists():
        return rand
    getRandomCode()


def makePagination(objs, itemsPerPage, request):
    paginator = Paginator(objs, itemsPerPage)
    page = request.GET.get('p', 1)
    try:
        page_obj = paginator.page(page)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)
    return page_obj.object_list, page_obj


def getTupleIndex(key, value):
    try:
        dictionary = dict(key).get(value)
        if dictionary:
            return dictionary
        return False
    except:
        return None


def getUserinfoByUsername(username, value):
    if not isinstance(username, str): return None
    user = Users.objects.filter(username__iexact=username)
    if not user.exists(): return None
    if not value.lower() in [field.name for field in Users._meta.fields]: return None
    return next(iter(user.values(value).first().values()))


def getInfosOfUserByUsername(username, value):
    if not username:
        return None
    user = Users.objects.filter(username__iexact=username)
    if not user.exists():
        return None
    infos = Infos.objects.filter(user=user.first())
    if not infos.exists(): return None
    if not value.lower() in [field.name for field in Infos._meta.fields]: return None
    return next(iter(infos.values(value).first().values()))


def sendEmail(subject: str, message: str, recipients: list, request) -> bool:
    try:
        emailInfo = Emailsending.objects.all()
        if not emailInfo.exists():
            return None
        username, password = emailInfo.values('collectionusername', 'collectionpassword').first().values()
        f = open("Alvand/templates/email-temp.html", 'r', encoding='utf-8')
        msg = f.read()
        msg = msg.replace("{{ message }}", message)
        return send_mail(subject, message, recipient_list=recipients, html_message=msg, from_email=username,
                         auth_user=username, auth_password=password) > 0
    except Exception as err:
        errMsg = None
        if isinstance(err.args[0], bytes) and "invalid address" in err.args[0].decode().lower():
            errMsg = f"آدرس ارسال شده برای ارسال ایمیل نامعتبر می باشد ({err.args[0].lower().replace("invalid address ", "").replace('"', "")})"
        else:
            if isinstance(err.args[1], bytes) and "username and password not accepted. for more information" in \
                    err.args[1].decode().lower():
                errMsg = f"نام کاربری و رمز عبور برای ارسال ایمیل نامعتبر است\nلطفا با مدیریت خود جهت تصحیح نام کاربری و رمز عبور در ارتباط باشید."
        log(request, logErrCodes.emails, logMessages.emailCouldnotSend.format(err, ))
        return errMsg or False


def hasAccess(access, redirectTo=None, request=None):
    if request:
        res = _check_access(access, redirectTo, request)
        if isinstance(res, HttpResponseRedirect):
            return res
        return True

    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(self, request, *args, **kwargs):
            res = _check_access(access, redirectTo, request)
            if isinstance(res, HttpResponseRedirect):
                return res
            return view_func(self, request, *args, **kwargs)

        return wrapped_view

    return decorator


def _check_access(access, redirectTo, request):
    username = checkSession(request)
    if not username:
        messages.error(request, messagesTypes.notlogin)
        return redirect(reverse_lazy("login"))

    user = Users.objects.filter(username__iexact=username)
    if not user.exists():
        messages.error(request, messagesTypes.userInfoNotFound)
        return redirect(reverse_lazy("login"))

    perm = Permissions.objects.filter(user=user.first())
    if not perm.exists():
        messages.error(request, messagesTypes.permissionsNotFound)
        return redirect(reverse_lazy("dashboard" if not redirectTo else redirectTo))

    if access.lower() not in ['view', 'write', 'modify', 'delete']:
        messages.error(request, messagesTypes.permissionsTypeNotFound)
        return redirect(reverse_lazy("dashboard" if not redirectTo else redirectTo))

    if user.first().groupname.lower() == "member":
        messages.error(request, messagesTypes.accessDeniedUser)
        return redirect(reverse_lazy("dashboard" if not redirectTo else redirectTo))

    can_access = perm.values(f'can_{access.lower()}').first()
    if not can_access or not can_access[f'can_{access.lower()}']:
        messages.error(request, messagesTypes.accessDeniedView if access.lower() == "view" else
        messagesTypes.accessDeniedWrite if access.lower() == "write" else
        messagesTypes.accessDeniedModify if access.lower() == "modify" else
        messagesTypes.accessDeniedDelete)
        return redirect(reverse_lazy("dashboard" if not redirectTo else redirectTo))

    return True

def check_active(username):
    active = Users.objects.filter(username__iexact=username)
    if active.exists():
        return active.first().active
    return False

def check_groupname(groupname):
    if groupname.lower() == 'user':
        return 'admin'
    elif groupname.lower() == 'admin':
        return 'superadmin'
    elif groupname.lower() == 'superadmin':
        return 'supporter'
    elif groupname.lower() == 'supporter':
        return 'supporter'
    else:
        return None

def getVersion():
    ver = lices.objects.all()
    if not ver.exists():return None
    return getTupleIndex(VERSIONS, ver.first().version)

def systemSettingsConfiguration(field, fieldRequest, request, success_url, form, isEthernet):

    if not isEthernet:
        if not field.get('device'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('flow'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('stopbits'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('baudrate'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('parity'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('databits'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('number_of_lines'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)

        if field.get('device') not in ['KX-TA308', 'KX-TES824', 'KX-TEM824']:
            messages.error(request, messagesTypes.fillNotInFields.format('تنطیمات دستگاه', ))
            return redirect(success_url)
        if field.get('flow') not in ['None', 'XON/XOFF', 'RTS/CTS', 'DSR/DTR']:
            messages.error(request, messagesTypes.fillNotInFields.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if field.get('stopbits') not in [1, 1.5, 2]:
            messages.error(request, messagesTypes.fillNotInFields.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if field.get('baudrate') not in [4800, 9600, 19200, 38400, 57600, 112500, 230400, 460800,
                                            921600]:
            messages.error(request, messagesTypes.fillNotInFields.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if field.get('parity') not in ['None', 'Odd', 'Even', 'Mark', 'Space']:
            messages.error(request, messagesTypes.fillNotInFields.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if field.get('databits') not in [5, 6, 7, 8]:
            messages.error(request, messagesTypes.fillNotInFields.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('number_of_lines').isdigit():
            messages.error(request, messagesTypes.fillNotInFields.format('تنظیمات دستگاه', ))
            return redirect(success_url)

        dev = Device.objects.all()
        if dev.exists():
            dev.update(device=field.get('device'), flow=field.get('flow'), stopbits=field.get('stopbits'),
                        baudrate=field.get('baudrate'),
                        parity=field.get('parity'), databits=field.get('databits'),
                        number_of_lines=field.get('number_of_lines'))
            log(request, logErrCodes.systemSettings, logMessages.updatedSettings.format('تنظیمات دستگاه'),
                checkSession(request))
        else:
            form.save()
            log(request, logErrCodes.systemSettings, logMessages.createdSettings.format('تنظیمات دستگاه'),
                checkSession(request))
    else:
        if not field.get('device'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('smdrip'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('smdrport'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('smdrpassword'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('number_of_lines'):
            messages.error(request, messagesTypes.fillAllFieldsInSection.format('تنظیمات دستگاه', ))
            return redirect(success_url)

        if field.get('device') not in ['KX-NS300', 'KX-NS500', 'KX-NS700', 'KX-NS1000', 'KX-HTS32', 'KX-HTS824']:
            messages.error(request, messagesTypes.fillNotInFields.format('تنطیمات دستگاه', ))
            return redirect(success_url)
        if not any( x.isdigit() for x in field.get('smdrip').split('.')):
            messages.error(request, messagesTypes.fillNotInFields.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('smdrport').isdigit():
            messages.error(request, messagesTypes.fillNotInFields.format('تنظیمات دستگاه', ))
            return redirect(success_url)
        if not field.get('number_of_lines').isdigit():
            messages.error(request, messagesTypes.fillNotInFields.format('تنظیمات دستگاه', ))
            return redirect(success_url)


        dev = Device.objects.all()
        if dev.exists():
            dev.update(device=field.get('device'), flow=None, stopbits=None,
                        baudrate=None,
                        parity=None, databits=None,
                        number_of_lines=field.get('number_of_lines'), smdrip=field.get('smdrip'), smdrport=field.get('smdrport'), smdrpassword=field.get('smdrpassword'))
            log(request, logErrCodes.systemSettings, logMessages.updatedSettings.format('تنظیمات دستگاه'),
                checkSession(request))
        else:
            form.save()
            log(request, logErrCodes.systemSettings, logMessages.createdSettings.format('تنظیمات دستگاه'),
                checkSession(request))

    return "تنظیمات دستگاه\n"



class verificationType:
    email = 0
    phone = 1


class messagesTypes:
    notlogin = "شما هنوز وارد حساب کاربری خود نشده اید."
    login = "کاربر {}، به حساب خود خوش آمدید"
    loggedIn = "شما از قبل وارد حساب خود شده اید."
    logout = "شما با موفقیت از حساب خود خارج شدید."
    invalidUsernameOrPassword = 'رمز عبور و یا نام کاربری نا معتبر است.'
    userInfoNotFound = "مشخصات کاربری شما یافت نشد."
    permissionsNotFound = "دسترسی یافت نشد."
    permissionsTypeNotFound = "نوع دسترسی نامعتبر است."
    accessDeniedUser = "شما مجاز به اقدام/انجام عملیات در قسمت های مدیریتی نیستید."
    accessDeniedView = "شما دسترسی دیدن این قسمت را ندارید."
    accessDeniedWrite = "شما دسترسی ایجاد کردن در این قسمت را ندارید."
    accessDeniedModify = "شما دسترسی ویرایش کردن در این قسمت را ندارید."
    accessDeniedDelete = "شما دسترسی حذف کردن در این قسمت را ندارید."
    fillAllFields = "لطفا تمامی مقادیر را کامل نمایید."
    fillAllFieldsInSection = "لطفا تمامی مقادیر را در {} کامل کنید."
    fillNotInFields = "مقدار انتخاب شده شما در {} نامعتبر است."
    licenseExpiredOrNotValid = "اعتبار لایسنس شما منقضی و یا معتبر نمی باشد."
    deAvtive = "حساب شما غیرفعال است"


class logErrCodes:
    logInOut = 0x21F1  # Login and logout
    emails = 0x211F  # Emails
    userSettings = 0x3F2  # User Settings
    systemSettings = 0x3F3  # System Settings
    license = 0x4F1
    others = 0xFFF0


class logMessages:
    loggedIn = "کاربر {} وارد حساب خود شد."
    loggedOut = "کاربر {} از حساب خود خارج شد."
    userProcessEmail = "کاربر {} اقدام به تایید ایمیل خود کرد"
    emailVerifyCodeSent = "کد با موفقیت برای کاربر {} با ایمیل {} ارسال شد."
    emailVerified = "ایمیل کاربر {} با موفقیت تایید شد."
    emailCouldnotSend = "ارسال ایمیل با خطا مواجه شد. ({})"
    createdSettings = "{} با موفقیت ثبت شد."
    updatedSettings = "{} با موفقیت بروزرسانی شد."
    dataDidnotSend = "داده ها ارسال نشد."
    dataSent = "داده ها با موفقیت ارسال شد."
    licenseExp = messagesTypes.licenseExpiredOrNotValid


def home(request):
    # Call setup_initial_data on first access to initialize the database
    setup_initial_data()
    return redirect('/dashboard')

def licenseNotActive(request):
    return render(request, "license.html", context={'pageTitle': 'لایسنس شما فعال نمی باشد', "hwid": getHWID()})

class systemSettings(FormView, View):
    template_name = 'settings.html'
    model = Device
    form_class = DeviceForm
    success_url = reverse_lazy('settings')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["pageTitle"] = 'تنظیمات سیستمی'
        context["deviceform"] = self.form_class
        context["contactInfo"] = ContactInfoForm()
        context['costForm'] = costsForm()
        context["extGroup"] = extGroups()
        context["groupname"] = Extensionsgroups.objects.all()
        context['emailSendingForm'] = emailSendingForm()
        context['userAccessToErrorsPageForm'] = userAccessToErrorsPageForm()
        context['users'] = Users.objects.exclude(groupname__in=['superadmin', 'supporter'])
        context['errors'] = Emailsending.objects.all().first().errors if Emailsending.objects.all().exists() else None
        context['emailset'] = Emailsending.objects.all() if Emailsending.objects.all().exists() else None
        context['contactInfos'] = ContactInfo.objects.all() if ContactInfo.objects.all().exists() else None
        context['devices'] = Device.objects.all() if Device.objects.all().exists() else None
        context['costs'] = Costs.objects.all() if Costs.objects.all().exists() else None
        context['version'] = getVersion()
        return context

    @hasAccess('view')
    def get(self, request, *args, **kwargs):
        if not checkSession(self.request):
            messages.error(self.request, messagesTypes.notlogin)
            return redirect(reverse_lazy('login'))
        if not check_active(checkSession(request)):
            messages.error(request, messagesTypes.deAvtive)
            return redirect(reverse_lazy('logout' if checkSession(request) else 'login'))
        context = self.get_context_data()
        return render(request, self.template_name, context)

    def form_valid(self, form):
        if not checkSession(self.request):
            messages.error(self.request, messagesTypes.notlogin)
            return redirect(reverse_lazy('login'))
        if not check_active(checkSession(self.request)):
            messages.error(self.request, messagesTypes.deAvtive)
            return redirect(reverse_lazy('logout' if checkSession(self.request) else 'login'))
        if isinstance(check := hasAccess("view", "settings", self.request), HttpResponseRedirect):
            return check
        if isinstance(check := hasAccess("write", "settings", self.request), HttpResponseRedirect):
            return check
        if isinstance(check := hasAccess("modify", "settings", self.request), HttpResponseRedirect):
            return check
        msgs = "تنظیمات زیر با موفقیت ذخیره شد:\n"
        field = form.cleaned_data
        fieldRequest = self.request.POST
        if any(f for f in [field.get('device'), field.get('flow'), field.get('stopbits'), field.get('baudrate'),
                            field.get('parity'), field.get('databits'), field.get('number_of_lines')]) or any (f for f in [field.get('smdrip'),
                            field.get('smdrport'), field.get('smdrpassword')]):
            if getVersion() == 'alvand':
                systemSettingsConfiguration(field, fieldRequest, self.request, self.success_url, form, False)
            elif getVersion == 'binalud':
                isEthernet = False
                if field.get('device') in ['KX-TDA30','KX-TDA100','KX-TDA100D','KX-TDA100DBA','KX-TDA200','KX-TDA600']:
                    isEthernet = False
                elif field.get('device') in ['KX-NS300', 'KX-NS500', 'KX-NS700', 'KX-NS1000', 'KX-HTS32', 'KX-HTS824']:
                    isEthernet = True
                else:
                    cable = field.get('cable_type')
                    if not cable or cable.lower() == 'none':
                        messages.error(self.request, 'لطفا یکی از انواع کابل هارا انتخاب کنید.')
                        return redirect(self.success_url)
                    elif cable.lower() == 'rs232c':
                        isEthernet = False
                    else:
                        isEthernet = True

                systemSettingsConfiguration(field, fieldRequest, self.request, self.success_url, form, isEthernet)

            else:
                messages.error(self.request, 'نسخه برنامه شما هنوز مشخص نشده است.')
                return redirect(self.success_url)

        if any(f for f in [fieldRequest.get('province'), fieldRequest.get('phone_number')]):
            if not fieldRequest.get('province'):
                messages.error(self.request, messagesTypes.fillAllFieldsInSection.format('اطلاعات تماس'))
                return redirect(self.success_url)
            if not fieldRequest.get('phone_number'):
                messages.error(self.request, messagesTypes.fillAllFieldsInSection.format('اطلاعات تماس'))
                return redirect(self.success_url)
            if not fieldRequest.get('province').isdigit():
                messages.error(self.request, messagesTypes.fillNotInFields.format('انتخاب استان'))
                return redirect(self.success_url)
            if fieldRequest.get('province') not in ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
                                                    '11', '12', '13', '14', '15', '16', '17', '18', '19', '20',
                                                    '21', '22', '23', '24', '25', '26', '27', '28', '29', '30']:
                messages.error(self.request, messagesTypes.fillNotInFields.format('انتخاب استان'))
                return redirect(self.success_url)
            if not fieldRequest.get('phone_number').isdigit():
                messages.error(self.request, messagesTypes.fillNotInFields.format('شماره همراه مدیر'))
                return redirect(self.success_url)
            if len(fieldRequest.get('phone_number')) != 11:
                messages.error(self.request, messagesTypes.fillNotInFields.format('شماره همراه مدیر'))
                return redirect(self.success_url)

            conInfo = ContactInfo.objects.all()
            if conInfo.exists():
                conInfo.update(province=fieldRequest.get('province'), phone_number=fieldRequest.get('phone_number'),
                               user=Users.objects.filter(username__iexact=checkSession(self.request)).first())
                log(self.request, logErrCodes.systemSettings, logMessages.updatedSettings.format('اطلاعات تماس'),
                    checkSession(self.request))

            else:
                ContactInfo.objects.create(province=fieldRequest.get('province'),
                                           phone_number=fieldRequest.get('phone_number'), user=Users.objects.filter(
                        username__iexact=checkSession(self.request)).first())
                log(self.request, logErrCodes.systemSettings, logMessages.createdSettings.format('اطلاعات تماس'),
                    checkSession(self.request))
            msgs += "اطلاعات تماس\n"
        if fieldRequest.get('edit') or fieldRequest.get('delete') or fieldRequest.get('add'):
            editext = Extensionsgroups.objects.filter(label__iexact=fieldRequest.get('label'))

            if not any(f for f in [any(x for x in fieldRequest.getlist('exts')), fieldRequest.get('label')]):
                messages.error(self.request, messagesTypes.fillAllFields)
                return redirect(self.success_url)
            if fieldRequest.get('edit'):
                if not editext.exists():
                    messages.error(self.request, 'این نام گروه وجود ندارد.')
                    return redirect(self.success_url)
                getExtFromDB = editext.values_list('exts', flat=True).first() or []
                valuesFromUser = [int(x) for x in fieldRequest.getlist('exts')]
                mightRemove = set(getExtFromDB) - set(valuesFromUser)
                mightAdd = set(valuesFromUser) - set(getExtFromDB)
                mergeAndUpdate = list((set(getExtFromDB) - mightRemove) | mightAdd)
                editext.update(exts=mergeAndUpdate, label=fieldRequest.get('label'),
                               modifyby=Users.objects.filter(username__iexact=checkSession(self.request)).first())
                msgs += "ویرایش تنظیمات دسترسی به گروه ها\n"

            if fieldRequest.get('add'):
                if editext.exists():
                    messages.error(self.request, 'این نام گروه وجود دارد.')
                    return redirect(self.success_url)
                if len(fieldRequest.get('label')) <= 3 and len(fieldRequest.get('label')) >= 25:
                    messages.error(self.request, 'نام گروه باید بین ۳ تا ۲۵ حرف باشد.')
                    return redirect(self.success_url)

                Extensionsgroups.objects.create(label=fieldRequest.get('label'),
                                                exts=[int(x) for x in fieldRequest.getlist('exts')],
                                                modifyby=Users.objects.filter(
                                                    username__iexact=checkSession(self.request)).first())
                msgs += "اضافه تنظیمات دسترسی به گروه ها\n"
            if fieldRequest.get('delete'):
                lbl = fieldRequest.get('label')
                editext = Extensionsgroups.objects.filter(label__iexact=lbl)
                if not editext.exists():
                    messages.error(self.request, 'این نام گروه وجود ندارد.')
                    return redirect(self.success_url)
                perms = Permissions.objects.filter(exts_label__contains=[lbl])
                if perms.exists():
                    for perm in perms:
                        if lbl in perm.exts_label:
                            updateLbl = [label for label in perm.exts_label if label != lbl]
                            perm.exts_label = updateLbl
                            perm.save()
                editext.delete()
                msgs += f"حذف گروه {lbl} از تنظیمات دسترسی به گروه ها\n"

        if any(cost for cost in
               [fieldRequest.get("hamrahaval"), fieldRequest.get("irancell"), fieldRequest.get("rightel"),
                fieldRequest.get("provincial"), fieldRequest.get("international"),
                fieldRequest.get("outofprovincial")]):
            if not fieldRequest.get("hamrahaval") or not fieldRequest.get("irancell") or not fieldRequest.get(
                    "rightel") or not fieldRequest.get("provincial") or not fieldRequest.get(
                "international") or not fieldRequest.get("outofprovincial"):
                messages.error(self.request, messagesTypes.fillAllFieldsInSection.format('هزینه های تماس'))
                return redirect(self.success_url)
            fields = [fieldRequest.get("hamrahaval"), fieldRequest.get("irancell"), fieldRequest.get("rightel"),
                      fieldRequest.get("provincial"), fieldRequest.get("international"),
                      fieldRequest.get("outofprovincial")]
            if any(not isfloat(item) for item in fields):
                messages.error(self.request, "لطفا مقادیر هزینه های تماس را به صورت اعشار و یا عدد صحیح بنویسید.")
                return redirect(self.success_url)
            if any(float(item) < 0.0 for item in fields):
                messages.error(self.request, "هزینه های تماس نباید کمتر از 0 باشد.")
                return redirect(self.success_url)
            costs = Costs.objects
            if costs.all().exists():
                costs.update(hamrahaval=fieldRequest.get('hamrahaval'), irancell=fieldRequest.get('irancell'),
                             rightel=fieldRequest.get('rightel'),
                             provincial=fieldRequest.get('provincial'), international=fieldRequest.get('international'),
                             outofprovincial=fieldRequest.get('outofprovincial'), updated_at=timezone.now())
                log(self.request, logErrCodes.systemSettings, logMessages.updatedSettings.format('هزینه های تماس'),
                    checkSession(self.request))
            else:
                costs.create(hamrahaval=fieldRequest.get('hamrahaval'), irancell=fieldRequest.get('irancell'),
                             rightel=fieldRequest.get('rightel'),
                             provincial=fieldRequest.get('provincial'), international=fieldRequest.get('international'),
                             outofprovincial=fieldRequest.get('outofprovincial'), created_at=timezone.now())
                log(self.request, logErrCodes.systemSettings, logMessages.createdSettings.format('هزینه های تماس'),
                    checkSession(self.request))
            msgs += "هزینه های تماس\n"
        if any(f for f in [fieldRequest.get('collectionusername'), fieldRequest.get('collectionpassword'),
                           fieldRequest.get('emailtosend'), any(err for err in fieldRequest.getlist('errors'))]):
            if not all(f for f in [fieldRequest.get('collectionusername'), fieldRequest.get('collectionpassword'),
                                   fieldRequest.get('emailtosend'),
                                   any(err for err in fieldRequest.getlist('errors'))]):
                messages.error(self.request, messagesTypes.fillAllFieldsInSection.format('تنظیمات ایمیل'))
                return redirect(self.success_url)
            if not all("@gmail.com" in x.strip() for x in
                       [fieldRequest.get('collectionusername'), fieldRequest.get('emailtosend')]):
                messages.error(self.request, "ایمیل باید حتما دارای @gmail.com باشد. (مانند <EMAIL>)")
                return redirect(self.success_url)
            if any(len(x.strip().replace("@gmail.com", "")) < 3 for x in
                   [fieldRequest.get('collectionusername'), fieldRequest.get('emailtosend')]):
                messages.error(self.request, "ایمیل وارد شده نامعتبر است.")
                return redirect(self.success_url)
            if not fieldRequest.get('lines').isdigit() and not int(fieldRequest.get('lines')) in range(1, 10 + 1):
                messages.error(self.request, messagesTypes.fillNotInFields.format('تعداد خطاها برای ارسال'))
                return redirect(self.success_url)
            if not any(item.isdigit() for item in fieldRequest.getlist('errors')):
                messages.error(self.request, messagesTypes.fillNotInFields.format('خطاها'))
                return redirect(self.success_url)
            if not any(
                    Errors.objects.filter(errorcodenum=int(item)).exists() for item in fieldRequest.getlist('errors')):
                messages.error(self.request, "یکی از خطا های انتخاب شده وجود ندارد.")
                return redirect(self.success_url)
            emailSendCheck = Emailsending.objects
            if emailSendCheck.all().exists():
                emailSendCheck.update(errors=[int(x) for x in fieldRequest.getlist('errors')],
                                      emailtosend=fieldRequest.get('emailtosend'),
                                      collectionusername=fieldRequest.get('collectionusername'),
                                      collectionpassword=fieldRequest.get('collectionpassword'),
                                      lines=int(fieldRequest.get('lines')),
                                      byadmin=Users.objects.filter(username__iexact=checkSession(self.request)).first(),
                                      updated_at=timezone.now())
                log(self.request, logErrCodes.systemSettings, logMessages.updatedSettings.format('تنظیمات ایمیل'),
                    checkSession(self.request))

            else:
                emailSendCheck.create(errors=[int(x) for x in fieldRequest.getlist('errors')],
                                      emailtosend=fieldRequest.get('emailtosend'),
                                      collectionusername=fieldRequest.get('collectionusername'),
                                      collectionpassword=fieldRequest.get('collectionpassword'),
                                      lines=int(fieldRequest.get('lines')),
                                      byadmin=Users.objects.filter(username__iexact=checkSession(self.request)).first(),
                                      created_at=timezone.now())
                log(self.request, logErrCodes.systemSettings, logMessages.createdSettings.format('تنظیمات ایمیل'),
                    checkSession(self.request))
            msgs += "تنظیمات ایمیل\n"
        if fieldRequest.get('users') and fieldRequest.get("users").lower() != "none":
            user = Users.objects.filter(username__iexact=fieldRequest.get('users'))
            if not user.exists():
                messages.error(self.request, f"کاربر {fieldRequest.get('users')} وجود ندارد.")
                return redirect(self.success_url)
            perm = Permissions.objects.filter(user=user.first())
            if not perm.exists():
                Permissions.objects.create(user=user.first(), errorsreport=bool(fieldRequest.get('errorsreport')))
            else:
                perm.update(errorsreport=bool(fieldRequest.get('errorsreport')))
            log(self.request, logErrCodes.systemSettings,
                logMessages.updatedSettings.format('دسترسی به گزارش خطا ها - گروه بندی دسترسی ها'),
                checkSession(self.request))
            msgs += "دسترسی به گزارش خطا ها - گروه بندی دسترسی ها\n"
        if msgs != "تنظیمات زیر با موفقیت ذخیره شد:\n":
            messages.success(self.request, msgs)
        else:
            messages.info(self.request, "هیچ تغییراتی اعمال نشده است.")
        return redirect(self.success_url)

    def form_invalid(self, form):
        if not checkSession(self.request):
            messages.error(self.request, messagesTypes.notlogin)
            return redirect(self.success_url)
        if not check_active(checkSession(self.request)):
            messages.error(self.request, messagesTypes.deAvtive)
            return redirect(reverse_lazy('logout' if checkSession(self.request) else 'login'))
        if isinstance(check := hasAccess("write", "user", self.request), HttpResponseRedirect):
            return check
        messages.error(self.request, messagesTypes.fillAllFields)
        return redirect(self.success_url)


class dashboardPage(TemplateView, View):
    template_name = "dashboard.html"

    def get(self, request, *args, **kwargs):
        if not checkSession(request):
            messages.error(request, messagesTypes.notlogin)
            return redirect(reverse_lazy("login"))
        if not check_active(checkSession(request)):
            messages.error(request, messagesTypes.deAvtive)
            return redirect(reverse_lazy('logout' if checkSession(request) else 'login'))
        context = self.get_context_data()
        if request.GET:
            dateFrom = request.GET.get('dateFrom')
            dateTo = request.GET.get('dateTo')
            urbanline = request.GET.getlist('urbanline')
            extline = request.GET.getlist('extline')
            calls = request.GET.getlist('calls')
            calls = persianCallTypeToEnglish(calls) if calls else None
            calltype = Q(calltype__in=calls) if calls else Q()
            urbanline = Q(urbanline__in=urbanline) if urbanline and "همه تماس ها" not in request.GET.getlist(
                'calls') else Q()
            extline = Q(extension__in=extline) if extline and "همه تماس ها" not in request.GET.getlist('calls') else Q()
            filterDate = None
            if dateFrom and dateTo:
                try:
                    convFrom = jdatetime.datetime.strptime(dateFrom.replace("/", "-").replace('از', '').strip(), "%Y-%m-%d").togregorian()
                    convTo = jdatetime.datetime.strptime(dateTo.replace("/", "-").replace('تا', '').strip(), "%Y-%m-%d").togregorian()
                except:
                    messages.error(request, "فرمت تاریخ ها اشتباه است.")
                    return redirect(request.path)
                if convTo < convFrom:
                    messages.warning(request, "تاریخ اول نباید بزرگ تر از تاریخ دوم باشد.")
                    return redirect(request.path)

                filterDate = Q(date__range=(convFrom, convTo))
            query = Q()
            if urbanline:
                query &= urbanline
            if calltype:
                query &= calltype
            if extline:
                query &= extline
            if filterDate:
                query &= filterDate
            faults, page_obj = makePagination(
                Records.objects.filter(query).order_by(
                    '-created_at') if any(
                    item for item in [urbanline, calltype, extline, filterDate]) else dashboardData(
                    checkSession(request)), 20, request)
            context['dashPage'] = page_obj
        return render(request, self.template_name, context=context)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['pageTitle'] = 'داشبورد'
        faults, page_obj = makePagination(dashboardData(checkSession(self.request)), 20, self.request)
        context['dashPage'] = page_obj
        context['urbanlines'] = getUrbanlinesThatUserAccessThem(checkSession(self.request))
        context['extlines'] = getExtensionlines(checkSession(self.request))
        return context


def support(request):
    return render(request, 'support.html', context={'pageTitle': 'پشتیبانی'})


def checkSession(request):
    if 'user' in request.session:
        return request.session['user']
    return False


def login(request, user):
    if 'user' not in request.session:
        request.session['user'] = user.lower()
        return request.session['user']
    return request.session['user']


def getIPAddress(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[-1].strip()
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def log(request, errCode, errMessage, byWho=None):
    user = Users.objects.filter(username__iexact=checkSession(request) if not isinstance(request, str) else request)
    if not user.exists():
        return False
    ip = getIPAddress(request)
    return bool(
        Log.objects.create(user=user.first(), userBackup=user.first().username, errCode=errCode, errMessage=errMessage,
                           byWho=byWho if byWho else "Lotus", ip=ip,
                           macAddress=getInfosOfUserByUsername(user.first().username, 'macaddress')))


class logout(TemplateView):
    def get(self, request, *args, **kwargs):
        if checkSession(request):
            log(request, logErrCodes.logInOut, logMessages.loggedOut.format(request.session['user'], ),
                request.session['user'])
            del request.session['user']
            messages.success(request, messagesTypes.logout)
        else:
            messages.error(request, messagesTypes.notlogin)
        return redirect(reverse_lazy('login'))


class Profile(TemplateView, View):
    template_name = "profile.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['pageTitle'] = "پروفایل کاربری"
        user = Users.objects.filter(username__iexact=str(checkSession(self.request)))
        context['user'] = user.first() if user.exists() else None
        return context

    def get(self, request, *args, **kwargs):
        context = self.get_context_data()
        if not checkSession(request):
            messages.warning(request, messagesTypes.userInfoNotFound)
            return redirect(reverse_lazy("login"))
        if not check_active(checkSession(request)):
            messages.error(request, messagesTypes.deAvtive)
            return redirect(reverse_lazy('logout' if checkSession(request) else 'login'))
        if not Users.objects.filter(username__iexact=checkSession(request)).first().email_verified_at:
            if Verifications.objects.filter(user=Users.objects.filter(username__iexact=checkSession(request)).first(),
                                            type=verificationType.email).exists():
                context['email'] = True
        else:
            if 'email' in context: del context['email']
        return render(request, self.template_name, context=context)

    def post(self, request):
        if not checkSession(request):
            messages.error(request, messagesTypes.notlogin)
            return redirect(reverse_lazy("login"))
        if not check_active(checkSession(request)):
            messages.error(request, messagesTypes.deAvtive)
            return redirect(reverse_lazy('logout' if checkSession(request) else 'login'))
        email = getUserinfoByUsername("email")
        user = Users.objects.filter(email__iexact=email)
        verEmail = request.POST.get("verifyEmail")
        resend = request.POST.get("resend")
        if verEmail or resend:
            context = self.get_context_data()
            if not email:
                messages.error(request, messagesTypes.userInfoNotFound)
                return redirect(reverse_lazy("profile"))
            if not user.exists():
                messages.error(request, messagesTypes.userInfoNotFound)
                return redirect(reverse_lazy("profile"))
            if user.first().email_verified_at:
                messages.error(request, "ایمیل شما از قبل تایید شده است.")
                return redirect(reverse_lazy("profile"))
            verModel = Verifications.objects.filter(type=verificationType.email, user=user.first())
            if verEmail and not resend:
                if verModel.exists():
                    context['email'] = True
                    messages.error(request, "کد تاییدیه ایمیل از قبل برای شما ارسال شده است.")
                    return render(request, self.template_name, context=context)
            elif not verEmail and resend:
                if not verModel.exists():
                    if 'email' in context:
                        del context['email']
                    messages.error(request, "شما هیچ فرایند تاییدیه ایمیلی ندارید.")
                    return render(request, self.template_name, context=context)
            rand = getRandomCode()
            sent = sendEmail("تاییدیه ایمیل", f"code: {rand}", [email], request=request)
            if sent is True:
                if verEmail and not resend:
                    Verifications.objects.create(type=verificationType.email,
                                                 user=user.first(), code=rand)
                    log(request, logErrCodes.emails, logMessages.userProcessEmail.format(user.first().username, ),
                        user.first().username)
                elif not verEmail and resend:
                    verModel.update(code=rand)
                log(request, logErrCodes.emails,
                    logMessages.emailVerifyCodeSent.format(user.first().username.capitalize(), email.capitalize()),
                    user.first().username)
                context['email'] = True
                messages.success(request,
                                 f"کد تاییدیه {'مجددا' if not verEmail and resend else ''} به ایمیل شما ارسال شد.\nتوجه داشته باشید که حتما پوشه اسپم چک شود.")
                return render(request, self.template_name, context=context)
            elif sent is not True and sent is not False:
                messages.error(request, sent)
            messages.error(request, "در ارسال ایمیل با مشکلی مواجه شده ایم\nلطفا با مدیر خود در ارتباط باشید.")
        verifyBtn = request.POST.get("verify")
        if verifyBtn:
            code = request.POST.get("code")
            if not user.exists():
                messages.error(request, messagesTypes.userInfoNotFound)
                return redirect(reverse_lazy("profile"))
            if not user.first().email_verified_at:
                messages.error(request, "ایمیل شما از قبل تایید شده است.")
                return redirect(reverse_lazy("profile"))
            context = self.get_context_data()
            if not code.isdigit():
                context['email'] = True
                messages.error(request, "کد وارد شده نامعتبر است.")
                return render(request, self.template_name, context=context)
            verObj = Verifications.objects.filter(user=user.first(), code=int(code), type=verificationType.email)
            if not verObj.exists():
                context['email'] = True
                messages.warning(request, "کد وارد شده اشتباه است.")
                return render(request, self.template_name, context=context)
            verObj.delete()
            Users.objects.filter(email__iexact=email).update(email_verified_at=timezone.now())
            log(request, logErrCodes.emails, logMessages.emailVerified.format(user.first().username.capitalize(), ),
                user.first().username)
            messages.success(request, "ایمیل شما با موفقیت تایید شد.")
            if 'email' in context:
                del context['email']
            return render(request, self.template_name, context=context)
        return redirect(reverse_lazy("profile"))


class userLogin(View):
    def get(self, request, *args, **kwargs):
        if checkSession(request):
            if not check_active(checkSession(request)):
                messages.error(request, messagesTypes.deAvtive)
                return redirect(reverse_lazy('logout' if checkSession(request) else 'login'))
            messages.error(request, messagesTypes.loggedIn)
            return redirect(reverse_lazy('dashboard'))
        return render(request, template_name="login.html", context={})

    def post(self, request):
        if checkSession(request):
            if not check_active(checkSession(request)):
                messages.error(request, messagesTypes.deAvtive)
                return redirect(reverse_lazy('logout' if checkSession(request) else 'login'))
            messages.error(request, messagesTypes.loggedIn)
            return redirect(reverse_lazy('dashboard'))

        username = request.POST['user']
        password = request.POST['pass']
        user = Users.objects.filter(username__iexact=username)
        if user.exists():
            if not check_active(user.first().username):
                messages.error(request, messagesTypes.deAvtive)
                return redirect(reverse_lazy('logout' if checkSession(request) else 'login'))
            if check_password(password, user.first().password):
                login(request, username)
                log(request, logErrCodes.logInOut, logMessages.loggedIn.format(user.first().username.capitalize(), ),
                    user.first().username)
                messages.success(request, messagesTypes.login.format(user.first().username.capitalize(), ))
                return redirect(reverse_lazy('dashboard'))
            messages.error(request, messagesTypes.invalidUsernameOrPassword)
            return redirect(request.path)
        else:
            messages.error(request, messagesTypes.invalidUsernameOrPassword)
            return redirect(request.path)


class errorsPage(TemplateView, View):
    template_name = "error.html"

    def get(self, request, *args, **kwargs):
        if not checkSession(request):
            messages.error(request, messagesTypes.notlogin)
            return redirect(reverse_lazy("login"))
        if not check_active(checkSession(request)):
            messages.error(request, messagesTypes.deAvtive)
            return redirect(reverse_lazy('logout' if checkSession(request) else 'login'))
        if isinstance(hasAccess("view", "profile", self.request),
                      HttpResponseRedirect) or not Permissions.objects.filter(
            user=Users.objects.filter(username__iexact=checkSession(request)).first()).first().errorsreport:
            messages.error(request, messagesTypes.permissionsNotFound)
            return redirect(reverse_lazy("profile"))
        context = self.get_context_data()
        if request.GET:
            dateFrom = request.GET.get('dateFrom')
            dateTo = request.GET.get('dateTo')
            if dateFrom and dateTo:
                try:
                    convFrom = jdatetime.datetime.strptime(dateFrom.replace("/", "-").replace('از', '').strip(), "%Y-%m-%d").togregorian()
                    convTo = jdatetime.datetime.strptime(dateTo.replace("/", "-").replace('تا','').strip(), "%Y-%m-%d").togregorian()
                except:
                    messages.error(request, "فرمت تاریخ ها اشتباه است.")
                    return redirect(request.path)
                if convTo < convFrom:
                    messages.warning(request, "تاریخ اول نباید بزرگ تر از تاریخ دوم باشد.")
                    return redirect(request.path)
                qs = Faults.objects.filter(created_at__range=(convFrom, convTo))
                if not qs.exists():
                    messages.error(request, f"در بین تاریخ های {dateFrom} و {dateTo} گزارش خطایی پیدا نشد.")
                    return redirect(request.path)
                faults, page_obj = makePagination(qs.order_by('-created_at'), 20, self.request)
                context['pages'] = page_obj
        return render(request, self.template_name, context=self.get_context_data())

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['pageTitle'] = 'گزارش خطا ها'
        faults, page_obj = makePagination(Faults.objects.order_by('-created_at'), 20, self.request)
        context['pages'] = page_obj
        return context


class UserForm(FormView, View):
    template_name = "userprofile.html"
    model = Users
    form_class = userProfileForm
    success_url = reverse_lazy("user")

    @hasAccess("view")
    def get(self, request, *args, **kwargs):
        if not checkSession(request):
            return redirect(reverse_lazy("user"))
        if not check_active(checkSession(request)):
            messages.error(request, messagesTypes.deAvtive)
            return redirect(reverse_lazy('logout' if checkSession(request) else 'login'))
        return render(request, self.template_name, context=self.get_context_data(**kwargs))

    def get_context_data(self, **kwargs):
        data = super().get_context_data(**kwargs)
        data['pageTitle'] = "تنظیمات کاربری"
        data['userform'] = self.form_class
        data['infosform'] = InfosForm()
        data['permform'] = PermissionsForm()
        getUser = getUserinfoByUsername(checkSession(self.request), "groupname")
        if str(getUser).lower() in ["superadmin", "supporter"]:
            data['users'] = Users.objects.exclude(groupname="supporter")
        else:
            data['users'] = Users.objects.exclude(groupname__in=['superadmin', 'supporter'])
        return data

    def form_valid(self, form):
        if not checkSession(self.request):
            messages.error(self.request, messagesTypes.notlogin)
            return redirect(self.success_url)
        if not check_active(checkSession(self.request)):
            messages.error(self.request, messagesTypes.deAvtive)
            return redirect(reverse_lazy('logout' if checkSession(self.request) else 'login'))
        if isinstance(check := hasAccess("view", "settings", self.request), HttpResponseRedirect):
            return check
        field = form.cleaned_data
        username = field.get('username')
        userReq = Users.objects.filter(username__iexact=checkSession(self.request))
        userChecker =  Users.objects.filter(username__iexact=username)
        getUserChecker = userChecker.first().groupname.lower()
        getUserReq = userReq.first().groupname.lower()
        hasAccessOrNot = False
        if not userReq.exists():
            messages.error(self.request, messagesTypes.userInfoNotFound)
            return redirect(reverse_lazy('login'))
        if not userChecker.exists():
            messages.error(self.request, '')
            return redirect(reverse_lazy('user'))
        if getUserReq == 'supporter':
            hasAccessOrNot = getUserChecker in ['superadmin', 'admin', 'user']
        elif getUserReq == 'superadmin':
            hasAccessOrNot = getUserChecker in ['admin', 'user']
        elif getUserReq == 'admin':
            hasAccessOrNot = getUserChecker == 'user'
        elif getUserReq == 'user':
            hasAccessOrNot = False
        if not hasAccessOrNot:
            messages.error(self.request, messagesTypes.permissionsNotFound)
            return redirect(reverse_lazy('user'))

        email = field.get('email')
        extension = field.get('extension')
        fieldReq = self.request.POST
        deleteUser = fieldReq.get('deleteUser')
        saveUser = fieldReq.get('saveUser')
        deleteProfile = fieldReq.get('deleteProfile')
        uploadPhoto = self.request.FILES.get('uploadPhoto')
        ChangePassword = fieldReq.get('ChangePassword')
        nationalcode = fieldReq.get('nationalcode')
        phonenumber = fieldReq.get('phonenumber')
        accountnumbershaba = fieldReq.get('accountnumbershaba')
        cardnumber = fieldReq.get('cardnumber')
        accountnumber = fieldReq.get('accountnumber')
        military = fieldReq.get('military')
        gender = fieldReq.get('gender')
        maritalstatus = fieldReq.get('maritalstatus')
        educationdegree = fieldReq.get('educationdegree')
        province = fieldReq.get('province')
        editOrAdd = field.get('editOrAdd') if field.get('editOrAdd') else fieldReq.get("editOrAdd").lower()
        infosFields = set(InfosForm().fields.keys())
        if username.lower() == checkSession(self.request) and str(getUserinfoByUsername(username, "groupname")) in ['superadmin', 'supporter']:
            infosFields.discard("groupname")
        if fieldReq.get('gender') and fieldReq.get('gender') == "1":
            infosFields = [x for x in infosFields if x != "military"]
        if any(not fieldReq.get(x) for x in infosFields):
            messages.error(self.request, messagesTypes.fillAllFields)
            return redirect(reverse_lazy("user"))
        if editOrAdd == "none":
            messages.error(self.request, messagesTypes.fillAllFields)
            return redirect(reverse_lazy("user"))
        if saveUser:
            if editOrAdd == 'add':
                if isinstance(check := hasAccess("write", "user", self.request), HttpResponseRedirect):
                    return check
                if len(nationalcode) > 10:
                    messages.error(self.request, 'کدملی نامعتبر است.')
                    return redirect(self.success_url)

                if len(phonenumber) != 11:
                    messages.error(self.request, 'شماره همراه نامعتبر است.')
                    return redirect(self.success_url)

                if len(accountnumbershaba) != 22:
                    messages.error(self.request, 'شماره شبا نامعتبر است.')
                    return redirect(self.success_url)

                if len(cardnumber) < 16 and len(cardnumber) > 19:
                    messages.error(self.request, 'شماره کارت نامعتبر است.')
                    return redirect(self.success_url)

                if not gender.isdigit():
                    messages.error(self.request, 'جنسیت نامعتبر است.')
                    return redirect(self.success_url)

                if "groupname" in form.cleaned_data and not form.cleaned_data['groupname'].lower() in ['superadmin', 'admin', 'member'] and not Groups.objects.filter(enname__iexact=form.cleaned_data['groupname']).exists():
                    messages.error(self.request, "نقش نامعتبر است.")
                    return redirect(self.success_url)

                if fieldReq.get('gender') not in ['0', '1', '2']:
                    messages.error(self.request, 'جنسیت نامعتبر است.')
                    return redirect(self.success_url)

                if field.get('gender') == '1':
                    military = None
                else:
                    military = fieldReq.get('military')

                if fieldReq.get('gender') and fieldReq.get('gender') != "1" and fieldReq.get('military') not in ['0',
                                                                                                                 '1',
                                                                                                                 '2',
                                                                                                                 '3',
                                                                                                                 '4']:
                    messages.error(self.request, 'وضعیت سربازی نامعتبر است.')
                    return redirect(self.success_url)

                if not maritalstatus.isdigit():
                    messages.error(self.request, 'وضعیت تاهل نامعتبر است.')
                    return redirect(self.success_url)

                if fieldReq.get('maritalstatus') not in ['0', '1']:
                    messages.error(self.request, 'وضعیت تاهل نامعتبر است.')
                    return redirect(self.success_url)

                if not educationdegree.isdigit():
                    messages.error(self.request, 'مدرک تحصیلی نامعتبر است.')
                    return redirect(self.success_url)

                if fieldReq.get('educationdegree') not in ['0', '1', '2', '3', '4', '5', '6']:
                    messages.error(self.request, 'مدرک تحصیلی نامعتبر است.')
                    return redirect(self.success_url)

                if not province.isdigit():
                    messages.error(self.request, 'استان نامعتبر است.')
                    return redirect(self.success_url)

                if fieldReq.get('province') not in ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
                                                    '11', '12', '13', '14', '15', '16', '17', '18', '19', '20',
                                                    '21', '22', '23', '24', '25', '26', '27', '28', '29', '30']:
                    messages.error(self.request, 'استان نامعتبر است.')
                    return redirect(self.success_url)

                if Users.objects.filter(username__iexact=username).exists():
                    messages.error(self.request, 'این نام کاربری موجود است.')
                    return redirect(self.success_url)

                if Users.objects.filter(email__iexact=email).exists():
                    messages.error(self.request, 'این ایمیل موجود است.')
                    return redirect(self.success_url)

                if Users.objects.filter(extension=extension).exists():
                    messages.error(self.request, 'این داخلی موجود است.')
                    return redirect(self.success_url)

                if Users.objects.filter(extension=int(extension)).exists():
                    messages.error(self.request, 'این داخلی موجود است.')
                    return redirect(self.success_url)

                if not nationalcode.isdigit():
                    messages.error(self.request, 'این کدملی موجود است.')
                    return redirect(self.success_url)

                if Infos.objects.filter(nationalcode=int(nationalcode)).exists():
                    messages.error(self.request, 'این کدملی موجود است.')
                    return redirect(self.success_url)

                if not phonenumber.isdigit():
                    messages.error(self.request, 'این شماره همراه موجود است.')
                    return redirect(self.success_url)

                if Infos.objects.filter(phonenumber=int(phonenumber)).exists():
                    messages.error(self.request, 'این شماره همراه موجود است.')
                    return redirect(self.success_url)

                if Infos.objects.filter(accountnumbershaba=accountnumbershaba).exists():
                    messages.error(self.request, 'این شماره شبا موجود است.')
                    return redirect(self.success_url)

                if not cardnumber.isdigit():
                    messages.error(self.request, 'این شماره کارت موجود است.')
                    return redirect(self.success_url)

                if Infos.objects.filter(cardnumber=int(cardnumber)).exists():
                    messages.error(self.request, 'این شماره کارت موجود است.')
                    return redirect(self.success_url)

                if not accountnumber.isdigit():
                    messages.error(self.request, 'این شماره حساب موجود است.')
                    return redirect(self.success_url)

                if Infos.objects.filter(accountnumber=int(accountnumber)).exists():
                    messages.error(self.request, 'این شماره حساب موجود است.')
                    return redirect(self.success_url)

                if uploadPhoto:

                    valid = validatePhotoExt(uploadPhoto.name)
                    if not valid:
                        messages.error(self.request, 'پسوند فایل ارسال شده نامعتبر میباشد.')
                        return redirect(self.success_url)
                    filename = f"{username.lower()}_photo{valid.lower()}"
                    filepath = os.path.join('Alvand/static/upload', filename)

                    with open(filepath, '+wb') as f:
                        for bt in uploadPhoto:
                            f.write(bt)
                    picurl = filename
                else:
                    picurl = 'avatar.png'
                user = form.save(commit=False)

                user.group = Groups.objects.filter(enname=form.cleaned_data['groupname']).first()
                user.groupname = form.cleaned_data['groupname'].lower()
                if picurl:
                    user.picurl = picurl
                listOfExts = fieldReq.getlist('usersextension')
                labels = []
                nonLabels = []
                if any(l for l in listOfExts):
                    for item in listOfExts:
                        item = item.strip()
                        if Extensionsgroups.objects.filter(label=str(item)).exists():
                            labels.append(item)
                        elif item.isdigit() and (
                                Users.objects.filter(extension=int(item)).exists() or Records.objects.filter(
                            extension=str(item)).exists()):
                            nonLabels.append(str(item))
                if nonLabels:
                    user.usersextension = nonLabels
                user.password = make_password("123456789")
                user.save()
                if labels:
                    Permissions.objects.create(user=user, can_view=bool(fieldReq.get("can_view")),
                                               can_write=bool(fieldReq.get("can_write")),
                                               can_modify=bool(fieldReq.get("can_modify")),
                                               can_delete=bool(fieldReq.get("can_delete")), exts_label=labels)
                else:
                    Permissions.objects.create(user=user, can_view=bool(fieldReq.get("can_view")),
                                               can_write=bool(fieldReq.get("can_write")),
                                               can_modify=bool(fieldReq.get("can_modify")),
                                               can_delete=bool(fieldReq.get("can_delete")))
                Infos.objects.create(user=user, gender=gender, nationalcode=nationalcode,
                                     birthdate=fieldReq.get('birthdate'),
                                     telephone=fieldReq.get('telephone'), phonenumber=phonenumber,
                                     maritalstatus=maritalstatus,
                                     military=military, educationfield=fieldReq.get('educationfield'),
                                     educationdegree=educationdegree,
                                     province=province, city=fieldReq.get('city'),
                                     accountnumbershaba=accountnumbershaba,
                                     cardnumber=cardnumber, accountnumber=accountnumber,
                                     address=fieldReq.get('address'))

                messages.success(self.request, f'کاربر {username} با موفقیت به جمع ما پیوست.')
            elif editOrAdd == 'edit':
                if isinstance(check := hasAccess("modify", "user", self.request), HttpResponseRedirect):
                    return check
                user = Users.objects.filter(username__iexact=username).first()
                if not user:
                    messages.error(self.request, 'کاربر مورد نظر موجود نیست.')
                    return redirect(reverse_lazy("user"))

                userInfo = Infos.objects.filter(user=user).first()
                if user.email.lower() != email.lower():
                    if Users.objects.filter(email__iexact=email).exists():
                        messages.error(self.request, 'این ایمیل موجود است.')
                        return redirect(self.success_url)

                if user.extension != int(extension):
                    if Users.objects.filter(extension=int(extension)).exists():
                        messages.error(self.request, 'این داخلی موجود است.')
                        return redirect(self.success_url)

                if not nationalcode.isdigit():
                    messages.error(self.request, 'لطفا کد ملی را به صورت اعداد بنویسید.')
                    return redirect(self.success_url)

                if userInfo.nationalcode != int(nationalcode):
                    if Infos.objects.filter(nationalcode=int(nationalcode)).exists():
                        messages.error(self.request, 'این کدملی موجود است.')
                        return redirect(self.success_url)

                if "groupname" in form.cleaned_data and not form.cleaned_data['groupname'].lower() in ['superadmin', 'admin', 'member'] and not Groups.objects.filter(enname__iexact=form.cleaned_data['groupname']).exists():
                    messages.error(self.request, "نقش نامعتبر است.")
                    return redirect(self.success_url)

                if not phonenumber.isdigit():
                    messages.error(self.request, 'لطفا شماره همراه را به صورت اعداد بنویسید.')
                    return redirect(self.success_url)
                if int(userInfo.phonenumber) != int(phonenumber):
                    if Infos.objects.filter(phonenumber=int(phonenumber)).exists():
                        messages.error(self.request, 'این شماره همراه موجود است.')
                        return redirect(self.success_url)

                if str(userInfo.accountnumbershaba) != str(accountnumbershaba):
                    if Infos.objects.filter(accountnumbershaba=accountnumbershaba).exists():
                        messages.error(self.request, 'این شماره شبا موجود است.')
                        return redirect(self.success_url)

                if not cardnumber.isdigit():
                    messages.error(self.request, 'لطفا شماره کارت را به صورت اعداد بنویسید.')
                    return redirect(self.success_url)

                if str(userInfo.cardnumber) != str(cardnumber):
                    if Infos.objects.filter(cardnumber=cardnumber).exists():
                        messages.error(self.request, 'این شماره کارت موجود است.')
                        return redirect(self.success_url)

                if not accountnumber.isdigit():
                    messages.error(self.request, 'لطفا شماره حساب را به صورت اعداد بنویسید.')
                    return redirect(self.success_url)

                if int(userInfo.accountnumber) != int(accountnumber):
                    if Infos.objects.filter(accountnumber=accountnumber).exists():
                        messages.error(self.request, 'این شماره حساب موجود است.')
                        return redirect(self.success_url)

                if len(nationalcode) > 10:
                    messages.error(self.request, 'کدملی نامعتبر است.')
                    return redirect(self.success_url)

                if len(phonenumber) > 11:
                    messages.error(self.request, 'شماره همراه نامعتبر است.')
                    return redirect(self.success_url)

                if len(accountnumbershaba) != 22:
                    messages.error(self.request, 'شماره شبا نامعتبر است.')
                    return redirect(self.success_url)

                if len(cardnumber) < 16 and len(cardnumber) > 19:
                    messages.error(self.request, 'شماره کارت نامعتبر است.')
                    return redirect(self.success_url)

                if not gender.isdigit():
                    messages.error(self.request, 'جنسیت نامعتبر است.')
                    return redirect(self.success_url)

                if fieldReq.get('gender') not in ['0', '1', '2']:
                    messages.error(self.request, 'جنسیت نامعتبر است.')
                    return redirect(self.success_url)

                if fieldReq.get('gender') and fieldReq.get('gender') != "1" and fieldReq.get('military') not in ['0',
                                                                                                                 '1',
                                                                                                                 '2',
                                                                                                                 '3',
                                                                                                                 '4']:
                    messages.error(self.request, 'وضعیت سربازی نامعتبر است.')
                    return redirect(self.success_url)

                if field.get('gender') == '1':
                    military = None
                else:
                    military = fieldReq.get('military')

                if not maritalstatus.isdigit():
                    messages.error(self.request, 'وضعیت تاهل نامعتبر است.')
                    return redirect(self.success_url)

                if fieldReq.get('maritalstatus') not in ['0', '1']:
                    messages.error(self.request, 'وضعیت تاهل نامعتبر است.')
                    return redirect(self.success_url)

                if not educationdegree.isdigit():
                    messages.error(self.request, 'مدرک تحصیلی نامعتبر است.')
                    return redirect(self.success_url)

                if fieldReq.get('educationdegree') not in ['0', '1', '2', '3', '4', '5', '6']:
                    messages.error(self.request, 'مدرک تحصیلی نامعتبر است.')
                    return redirect(self.success_url)

                if not province.isdigit():
                    messages.error(self.request, 'استان نامعتبر است.')
                    return redirect(self.success_url)

                if fieldReq.get('province') not in ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
                                                    '11', '12', '13', '14', '15', '16', '17', '18', '19', '20',
                                                    '21', '22', '23', '24', '25', '26', '27', '28', '29', '30']:
                    messages.error(self.request, 'استان نامعتبر است.')
                    return redirect(self.success_url)
                listOfExts = fieldReq.getlist('usersextension')
                labels = []
                nonLabels = []
                if any(l for l in listOfExts):
                    for item in listOfExts:
                        item = item.strip()
                        if Extensionsgroups.objects.filter(label=str(item)).exists():
                            labels.append(item)
                        elif item.isdigit() and (
                                Users.objects.filter(extension=int(item)).exists() or Records.objects.filter(
                            extension=str(item)).exists()):
                            nonLabels.append(str(item))
                filename = "avatar.png"
                if uploadPhoto:
                    valid = validatePhotoExt(uploadPhoto.name)
                    if not valid:
                        messages.error(self.request, 'پسوند فایل ارسال شده نامعتبر میباشد.')
                        return redirect(self.success_url)
                    filename = f"{username.lower()}_photo{valid.lower()}"
                    filepath = os.path.join('Alvand/static/upload', filename)

                    with open(filepath, '+wb') as f:
                        for bt in uploadPhoto:
                            f.write(bt)
                checkuserobj = Users.objects.filter(username__iexact=username)
                gp = Groups.objects.filter(enname__iexact=form.cleaned_data['groupname']).first() if "groupname" in form.cleaned_data else None
                if uploadPhoto:
                    if checkuserobj.exists():
                        if username.lower() == checkSession(self.request) and str(getUserinfoByUsername(username, "groupname")) in ['superadmin', 'supporter']:
                            checkuserobj.update(name=field.get('name'),
                                                lastname=field.get('lastname'), extension=extension,
                                                email=email, usersextension=nonLabels, active=bool(field.get('active')),
                                                picurl=filename)
                        else:
                            checkuserobj.update(name=field.get('name'),
                                                lastname=field.get('lastname'), extension=extension,
                                                email=email, usersextension=nonLabels, active=bool(field.get('active')),
                                                picurl=filename, group=gp, groupname=form.cleaned_data['groupname'].lower())
                else:
                    if checkuserobj.exists():
                        if username.lower() == checkSession(self.request) and str(getUserinfoByUsername(username, "groupname")) in ['superadmin', 'supporter']:
                            checkuserobj.update(name=field.get('name'),
                                              lastname=field.get('lastname'), extension=extension,
                                               email=email, usersextension=nonLabels, active=bool(field.get('active')))
                        else:
                            checkuserobj.update(name=field.get('name'),
                                                lastname=field.get('lastname'), extension=extension,
                                                email=email, usersextension=nonLabels, active=bool(field.get('active')),
                                                group=gp, groupname=form.cleaned_data['groupname'].lower())
                if checkuserobj.exists():
                    perm = Permissions.objects.filter(user=checkuserobj.first())
                    if perm.exists():
                        perm.update(can_view=bool(fieldReq.get("can_view")), can_write=bool(fieldReq.get("can_write")),
                                    can_modify=bool(fieldReq.get("can_modify")),
                                    can_delete=bool(fieldReq.get("can_delete")), exts_label=labels)
                    inf = Infos.objects.filter(user=checkuserobj.first())
                    if inf.exists():
                        inf.update(gender=gender, nationalcode=nationalcode,
                                   birthdate=fieldReq.get('birthdate'),
                                   telephone=fieldReq.get('telephone'),
                                   phonenumber=phonenumber,
                                   maritalstatus=maritalstatus,
                                   military=military,
                                   educationfield=fieldReq.get(
                                       'educationfield'),
                                   educationdegree=educationdegree,
                                   province=province, city=fieldReq.get('city'),
                                   accountnumbershaba=accountnumbershaba,
                                   cardnumber=cardnumber,
                                   accountnumber=accountnumber,
                                   address=fieldReq.get('address'))

                messages.success(self.request, f'اطلاعات کاربر {username} با موفقیت بروز شد.')

            else:
                messages.error(self.request,
                               'برای انجام عملیات ویرایش یا اضافه کاربر باید گزینه مناسب را در فیلد ویرایش/اضافه انتخاب کنید.')
                return redirect(reverse_lazy("user"))
        elif deleteUser:
            if isinstance(check := hasAccess("delete", "user", self.request), HttpResponseRedirect):
                return check
            if editOrAdd == 'edit':
                username = form.cleaned_data.get('username')
                found_user = Users.objects.filter(username__iexact=username)
                if found_user.exists():
                    if username.lower() == checkSession(self.request) and str(getUserinfoByUsername(username, "groupname")) in ['superadmin', 'supporter']:
                        messages.error(self.request, "شما نمی توانید حساب کاربری بالاترین مقام را حذف کنید.")
                        return redirect(self.success_url)
                    Extensionsgroups.objects.filter(modifyby=found_user.first()).delete()
                    Infos.objects.filter(user=found_user.first()).delete()
                    Permissions.objects.filter(user=found_user.first()).delete()
                    Verifications.objects.filter(user=found_user.first()).delete()
                    filename = found_user.first().picurl
                    if filename in os.listdir('Alvand/static/upload'):
                        os.remove(f'Alvand/static/upload/{filename}')
                    found_user.delete()
                    messages.success(self.request, f'کاربر {username} با موفقیت حذف شد')
                    return redirect(self.success_url)
                else:
                    messages.error(self.request, f'کاربر {username} وجود ندارد')
                    return redirect(self.success_url)

            messages.error(self.request, 'برای حذف کاربر باید مقدار ویرایش را انتخاب کنید.')
        elif deleteProfile:
            if isinstance(check := hasAccess("delete", "user", self.request), HttpResponseRedirect):
                return check
            if editOrAdd == 'edit':
                username = form.cleaned_data.get('username')
                found_user_pro = Users.objects.filter(username__iexact=username)
                if found_user_pro.exists():
                    if found_user_pro.first().picurl.lower() == "avatar.png":
                        messages.error(self.request, f'کاربر {username} عکسی ندارد.')
                        return redirect(self.success_url)
                    else:
                        filename = found_user_pro.first().picurl
                        if filename in os.listdir('Alvand/static/upload'):
                            os.remove(f'Alvand/static/upload/{filename}')

                        found_user_pro.update(picurl='avatar.png')
                        messages.success(self.request, f'پروفایل کاربر {username} با موفقیت حذف شد.')
                        return redirect(self.success_url)
                else:
                    messages.error(self.request, f' کاربر {username} وجود ندارد.')
                    return redirect(self.success_url)
            else:
                messages.error(self.request, 'برای حذف پروفایل کاربر باید مقدار ویرایش را انتخاب کنید.')
                return redirect(self.success_url)
        elif ChangePassword:
            if editOrAdd == 'edit':
                username = form.cleaned_data.get('username')
                found_user = Users.objects.filter(username__iexact=username)
                if found_user.exists():
                    psw = 123456789
                    if check_password(psw, found_user.first().password):
                        messages.error(self.request, f'رمز عبور کاربر {username} قبلا بازنگاری شده است.')
                        return redirect(self.success_url)
                    Users.objects.update(password=make_password('123456789'))
                    messages.success(self.request, f'رمز عبور کاربر {username} با موفقیت بازنگاری شد.')
                    return redirect(self.success_url)
                else:
                    messages.error(self.request, f'کاربر {username} وجود ندارد.')
                    return redirect(self.success_url)

            messages.error(self.request, 'برای حذف پروفایل کاربر باید مقدار ویرایش را انتخاب کنید.')
            return redirect(self.success_url)
        else:
            if isinstance(check := hasAccess("view", "user", self.request), HttpResponseRedirect):
                return check
            messages.error(self.request, "درخواست ارسال شده نامعتبر است.")

        return super().form_valid(form)

    def form_invalid(self, form):
        if not checkSession(self.request):
            messages.error(self.request, messagesTypes.notlogin)
            return redirect(self.success_url)
        if not check_active(checkSession(self.request)):
            messages.error(self.request, messagesTypes.deAvtive)
            return redirect(reverse_lazy('logout' if checkSession(self.request) else 'login'))
        if isinstance(check := hasAccess("view", "settings", self.request), HttpResponseRedirect):
            return check
        if self.request.POST.get('deleteUser'):
            return self.form_valid(form)
        if self.request.POST.get('deleteProfile'):
            return self.form_valid(form)
        if self.request.POST.get(form):
            return self.form_valid('ChangePassword')
        if self.request.POST.get('saveUser') and str(self.request.POST.get('username')) == checkSession(self.request) and str(getUserinfoByUsername(checkSession(self.request), "groupname")) in ['superadmin', 'supporter']:
            return self.form_valid(form)
        messages.error(self.request, messagesTypes.fillAllFields)
        return redirect(reverse_lazy("user"))


def index(request):
    return redirect(reverse_lazy("dashboard"))

def setup_initial_data():
    errors = [
        (537, 'Change into Isolated mode', '• Malfunction occurred in Master unit or Backup Master unit.\n• Malfunction occurred in the communication path of Slave unit.', '• Check error log of Master unit or Backup Master unit.\n• Check all cable connections between the sites, and check that hubs, routers, etc. are operating correctly\n• Confirm that the communication transmission speed between sites is sufficient\n• Confirm that all other partiesُ equipment is powered on\n• Consult your network administrator'),
        (538, 'Isolated mode was released', 'Isolated mode was released.', 'This message shows that the operation mode recovered from Isolated mode.'),
        (539, 'VPN error', 'A communication error is occurring in VPN.', '• Check all cable connections between PBX and the other equipment connected via VPN, and check that hubs, routers, etc. are operating correctly\n• Confirm that the communication transmission speed between PBX and the other equipment connected via VPN is sufficient\n• Confirm that all other partiesُ equipment is powered on\n• Consult your network administrator'),
        (540, 'Network Security Alarm', 'Security issue such as DOS attacks occurred.', 'Consult your network administrator'),
        (541, 'NAS disconnected', '• NAS is not active\n• Network malfunction', '• Check all cable connections between the PBX and the NAS, and check that hubs, routers, etc. are operating correctly\n• Confirm that the communication transmission speed between the PBX and the NAS is sufficient\n• Confirm that all other equipment is powered on\n• Consult your network administrator'),
        (542, 'Not enough free space on NAS', '• Not enough memory space available to save the data\n• Wrong permission of the NAS', '• Remove unnecessary files from the NAS\n• Check the permission of the NAS')
    ]

    for error in errors:
        error_code_num, error_message, probable_cause, solution = error
        Errors.objects.get_or_create(
            errorcodenum=error_code_num,
            defaults={
                "errormessage": error_message,
                "probablecause": probable_cause,
                "solution": solution,
            },
        )

    telephones = [(1, None, 'آذربایجان', '+994', datetime.datetime(2025, 3, 6, 7, 58, 58, 880741, tzinfo=datetime.timezone.utc), None)]

    for item in telephones:
        _, type, name, code, _, _ = item
        
        Telephons.objects.get_or_create(
            code=code,
            defaults={
                'type': type,
                'name': name,
            }
        )

    get, created = Groups.objects.get_or_create(enname="supporter", pename="پشتیبانی")
    Groups.objects.get_or_create(enname="superadmin", pename="ابر مدیر")
    Groups.objects.get_or_create(enname="admin", pename="مدیر")
    Groups.objects.get_or_create(enname="member", pename="کاربر")

    getSup, createdSup = Users.objects.get_or_create(username="supporter", defaults={
        'password': make_password("DLqyS!5#dF13"),
        'group': get,
        'groupname': get.enname,
        'extension': -1,
        'lastname': 'الوند',
        'name': 'پشتیبانی',
        'email': '<EMAIL>',
        'email_verified_at': timezone.now()
    })

    Infos.objects.get_or_create(user=getSup, defaults={
        'birthdate': '2025/03/25',
        'phonenumber': '***********',
        'telephone': '********',
        'province': '10',
        'city': 'مشهد',
        'address': 'فرهنگ',
        'gender': '2',
        'military': '4',
        'maritalstatus': '1',
        'educationdegree': '6',
        'educationfield': 'IoT',
        'cardnumber': '****************',
        'accountnumber': '1234567',
        'accountnumbershaba': '640120020000009520896080',
        'nationalcode': '**********'
    })

    Permissions.objects.get_or_create(user=getSup, defaults={
        'perm_email': True,
        'can_view': True,
        'can_write': True,
        'can_modify': True,
        'can_delete': True,
        'errorsreport': True
    })
