{% load static baseTags %}
<!-- Desktop Sidebar -->
<aside id="sidebar"
       class="w-[135px] overflow-y-auto h-screen text-white z-40 fixed top-0 right-0 transform transition-all duration-300 ease-in-out sm:w-[135px] hidden lg:block">
    <div class="text-center border-b-[1px] m-1 mt-0">
        <img src="{% if 'user' in request.session %}{% if request.session.user|getBaseInfo:'picurl'|lower == 'avatar.png' %}{% static 'pic/' %}{% else %}{% static 'upload/' %}{% endif %}{{ request.session.user|getBaseInfo:'picurl' }}{% endif %}"
             alt="Profile" class="w-16 mx-auto border-white rounded-full border-2 object-cover">
        <p class="mt-3 text-xs sm:text-sm">
            {% if 'user' in request.session %}{{ request.session.user|capfirst }}{% endif %}</p>
        <p class="text-xs sm:text-sm mt-3">
            {% if 'user' in request.session %}{{ request.session.user|getBaseInfo:'extension' }}{% endif %}</p>
    </div>
    <nav class="mt-5 space-y-2">
        <a href="{% url 'dashboard' %}"
           class="py-3 text-xs sm:text-sm flex items-center justify-start">
            <i class="fas fa-tachometer-alt"></i> <span class="ml-2 sidebar-text sm:inline">داشبورد</span>
        </a>
     
        <a href="{% url 'profile' %}"
           class="py-3 text-xs sm:text-sm flex items-center justify-start">
            <i class="fas fa-user"></i> <span class="ml-2 sidebar-text sm:inline">پروفایل کاربری</span>
        </a>
        {% if request.session.user|getBaseInfo:"groupname" != "member" %}
        <div class="relative w-full">
            <button id="show-settings"
                    class="w-full text-center no-underline text-xs sm:text-sm p-3 flex items-center justify-start">
                <i class="fas fa-cog"></i> <span class="ml-2 sidebar-text sm:inline">تنظیمات</span>
            </button>
            <div id="dropdownmenu" class="absolute hidden right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white">
                <ul>
                    <li><a href="{% url 'user' %}"
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-users ml-2"></i>
                        <span class="ml-2 sidebar-text sm:inline">مدیریت کاربران</span>
                    </a></li>
                    <li><a href="{% url 'settings' %}"
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-cogs ml-2"></i>
                        <span class="ml-2 sidebar-text sm:inline">تنظیمات سیستمی</span>
                    </a></li>
                </ul>
            </div>
        </div>
        {% endif %}
        <a href="{% url 'errors' %}"
           class="py-3 text-xs sm:text-sm flex items-center justify-start">
            <i class="fas fa-exclamation-triangle"></i> <span class="ml-2 sidebar-text sm:inline">گزارش خطا</span>
        </a>
        <a href="{% url 'support' %}"
           class="py-3 text-xs sm:text-sm flex items-center justify-start">
            <i class="fas fa-question-circle"></i> <span class="ml-2 sidebar-text sm:inline">راهنما</span>
        </a>
        <button id="exit"
           class="w-full text-center no-underline text-xs sm:text-sm p-3 flex items-center justify-start">
            <i class="fas fa-sign-out-alt"></i> <span class="ml-2 sidebar-text sm:inline">خروج</span>
        </button>
    </nav>
</aside>

<!-- Mobile Sidebar Toggle Button -->
<button id="toggle-menu"
        class="fixed top-4 right-4 text-white p-2 rounded-md z-50 lg:hidden transition-all transform duration-300 ease-in-out bg-transparent mobile-only-element">
    <i id="menu-icon" class="fas fa-bars"></i>
</button>

<!-- Mobile Sidebar -->
<div id="mobile-sidebar"
     class="fixed overflow-y-auto top-0 right-0 w-[135px] h-screen text-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out"
     data-sidebar-state="closed">
    <div class="text-center border-b-[1px] m-1 mt-0">
        <img src="{% if 'user' in request.session %}{% if request.session.user|getBaseInfo:'picurl'|lower == 'avatar.png' %}{% static 'pic/' %}{% else %}{% static 'upload/' %}{% endif %}{{ request.session.user|getBaseInfo:'picurl' }}{% endif %}"
             alt="Profile" class="w-16 mx-auto border-white rounded-full border-2 object-cover">
        <p class="mt-3 text-xs sm:text-sm">
            {% if 'user' in request.session %}{{ request.session.user|capfirst }}{% endif %}</p>
        <p class="text-xs sm:text-sm mt-3">
            {% if 'user' in request.session %}{{ request.session.user|getBaseInfo:'extension' }}{% endif %}</p>
    </div>
    <nav class="mt-5 space-y-2">
        <a href="{% url 'dashboard' %}"
           class="py-3 text-xs sm:text-sm flex items-center justify-start">
            <i class="fas fa-tachometer-alt"></i> <span class="ml-2 sidebar-text sm:inline">داشبورد</span>
        </a>
        <a href="{% url 'smdr-dashboard' %}"
           class="py-3 text-xs sm:text-sm flex items-center justify-start">
            <i class="fas fa-phone-alt"></i> <span class="ml-2 sidebar-text sm:inline">داشبورد SMDR</span>
        </a>
        <a href="{% url 'profile' %}"
           class="py-3 text-xs sm:text-sm flex items-center justify-start">
            <i class="fas fa-user"></i> <span class="ml-2 sidebar-text sm:inline">پروفایل کاربری</span>
        </a>
        {% if request.session.user|getBaseInfo:"groupname" != "member" %}
        <div class="relative w-full">
            <button type="button" id="show-settings-mb"
                    class="w-full text-center no-underline text-xs sm:text-sm p-3 flex items-center justify-start">
                <i class="fas fa-cog"></i> <span class="ml-2 sidebar-text sm:inline">تنظیمات</span>
            </button>
            <div id="dropdownmenu-mb" class="absolute hidden right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white">
                <ul>
                    <li><a href="{% url 'user' %}"
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-users ml-2"></i>
                        <span class="ml-2 sidebar-text sm:inline">مدیریت کاربران</span>
                    </a></li>
                    <li><a href="{% url 'settings' %}"
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-cogs ml-2"></i>
                        <span class="ml-2 sidebar-text sm:inline">تنظیمات سیستمی</span>
                    </a></li>
                </ul>
            </div>
        </div>
        {% endif %}
        <a href="{% url 'errors' %}"
           class="py-3 text-xs sm:text-sm flex items-center justify-start">
            <i class="fas fa-exclamation-triangle"></i> <span class="ml-2 sidebar-text sm:inline">گزارش خطا</span>
        </a>
        <a href="{% url 'support' %}"
           class="py-3 text-xs sm:text-sm flex items-center justify-start">
            <i class="fas fa-question-circle"></i> <span class="ml-2 sidebar-text sm:inline">راهنما</span>
        </a>
        <button id="exit-mb"
           class="w-full text-center no-underline text-xs sm:text-sm p-3 flex items-center justify-start">
            <i class="fas fa-sign-out-alt"></i> <span class="ml-2 sidebar-text sm:inline">خروج</span>
        </button>
    </nav>
</div>

<!-- Exit Modal for Desktop -->
<div id="exitmodal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white dark:bg-[#0D1B2A] rounded-lg p-8 max-w-md w-full">
        <h2 class="text-xl font-bold mb-4 dark:text-[#F5F7FA]">آیا مطمئن هستید؟</h2>
        <p class="mb-6 dark:text-[#CFD8DC]">آیا می‌خواهید از حساب کاربری خود خارج شوید؟</p>
        <div class="flex justify-end space-x-4">
            <button id="close" class="px-4 py-2 bg-gray-200 dark:bg-[#37474F] text-gray-800 dark:text-[#F5F7FA] rounded-lg">انصراف</button>
            <a href="{% url 'logout' %}" class="px-4 py-2 bg-[#FF7043] text-white rounded-lg">خروج</a>
        </div>
    </div>
</div>

<!-- Exit Modal for Mobile -->
<div id="exitmodal-mb" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white dark:bg-[#0D1B2A] rounded-lg p-8 max-w-md w-full">
        <h2 class="text-xl font-bold mb-4 dark:text-[#F5F7FA]">آیا مطمئن هستید؟</h2>
        <p class="mb-6 dark:text-[#CFD8DC]">آیا می‌خواهید از حساب کاربری خود خارج شوید؟</p>
        <div class="flex justify-end space-x-4">
            <button id="close-mb" class="px-4 py-2 bg-gray-200 dark:bg-[#37474F] text-gray-800 dark:text-[#F5F7FA] rounded-lg">انصراف</button>
            <a href="{% url 'logout' %}" class="px-4 py-2 bg-[#FF7043] text-white rounded-lg">خروج</a>
        </div>
    </div>
</div>
